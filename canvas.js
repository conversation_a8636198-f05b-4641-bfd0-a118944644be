document.addEventListener('DOMContentLoaded', () => {
  // Éléments du DOM
  const canvas = document.getElementById('canvas');
  const canvasContainer = document.getElementById('canvas-container');
  const addPromptBlockButton = document.getElementById('add-prompt-block');
  const addImageBlockButton = document.getElementById('add-image-block');
  const saveCanvasButton = document.getElementById('save-canvas');
  const canvasNameInput = document.getElementById('canvas-name');
  const zoomInButton = document.getElementById('zoom-in');
  const zoomOutButton = document.getElementById('zoom-out');
  const resetViewButton = document.getElementById('reset-view');
  const geminiModelSelector = document.getElementById('gemini-model-selector');
  const promptBlockTemplate = document.getElementById('prompt-block-template');
  const imageBlockTemplate = document.getElementById('image-block-template');
  const templateStoryboardButton = document.getElementById('template-storyboard');

  // Variables pour le canvas
  let canvasId = null;
  let canvasName = 'Nouveau Canvas';
  let canvasData = {
    blocks: [],
    connections: []
  };
  let scale = 1;
  let offsetX = 0;
  let offsetY = 0;
  let isDragging = false;
  let startX, startY;
  let activeBlock = null;
  let nextBlockId = 1;

  // Variables pour le menu contextuel
  let contextMenu = null;

  // Initialiser le canvas
  initCanvas();

  // Initialiser le menu contextuel
  initContextMenu();

  // Charger les paramètres sauvegardés
  chrome.storage.local.get(['geminiModel'], (result) => {
    if (result.geminiModel) {
      geminiModelSelector.value = result.geminiModel;
      geminiAPI.setModel(result.geminiModel);
    }
  });

  // Sauvegarder les paramètres lorsqu'ils sont modifiés
  geminiModelSelector.addEventListener('change', () => {
    const selectedModel = geminiModelSelector.value;
    chrome.storage.local.set({ geminiModel: selectedModel });
    geminiAPI.setModel(selectedModel);
  });

  // Ajouter un bloc de prompt
  addPromptBlockButton.addEventListener('click', () => {
    createPromptBlock();
  });

  // Ajouter un bloc d'image
  addImageBlockButton.addEventListener('click', () => {
    createImageBlock();
  });

  // Créer un GIF animé à partir des images sélectionnées
  const createGifButton = document.getElementById('create-gif');
  createGifButton.addEventListener('click', () => {
    createGifFromSelectedImages();
  });

  // Template de storyboard avec 50 scènes
  templateStoryboardButton.addEventListener('click', () => {
    createStoryboardTemplate();
  });

  // Sauvegarder le canvas
  saveCanvasButton.addEventListener('click', () => {
    saveCanvas();
  });

  // Zoom et déplacement
  zoomInButton.addEventListener('click', () => {
    setZoom(scale * 1.2);
  });

  zoomOutButton.addEventListener('click', () => {
    setZoom(scale * 0.8);
  });

  resetViewButton.addEventListener('click', () => {
    resetView();
  });

  // Variables pour la sélection
  let selectedBlocks = [];
  let isSelecting = false;
  let selectionStartX, selectionStartY;
  let selectionBox = null;

  // Gestion du déplacement du canvas avec le bouton du milieu de la souris
  canvasContainer.addEventListener('mousedown', (e) => {
    // Utiliser uniquement le bouton du milieu (wheel button, button = 1)
    if (e.button === 1) {
      e.preventDefault(); // Empêcher le comportement par défaut du bouton du milieu

      isDragging = true;
      startX = e.clientX - offsetX;
      startY = e.clientY - offsetY;
      canvasContainer.style.cursor = 'grabbing';
    } else if (e.button === 0) {
      // Clic gauche pour la sélection
      // Ne pas démarrer la sélection si on clique sur un bloc
      if (e.target.closest('.prompt-block, .image-block')) {
        // Si on clique sur un bloc avec Ctrl, ajouter/retirer de la sélection
        if (e.ctrlKey) {
          const clickedBlock = e.target.closest('.prompt-block, .image-block');
          toggleBlockSelection(clickedBlock);
        }
        return;
      }

      // Démarrer la sélection
      isSelecting = true;
      selectionStartX = e.clientX;
      selectionStartY = e.clientY;

      // Créer la boîte de sélection
      if (!selectionBox) {
        selectionBox = document.createElement('div');
        selectionBox.className = 'selection-box';
        canvasContainer.appendChild(selectionBox);
      }

      // Positionner la boîte de sélection
      selectionBox.style.left = `${selectionStartX}px`;
      selectionBox.style.top = `${selectionStartY}px`;
      selectionBox.style.width = '0px';
      selectionBox.style.height = '0px';
      selectionBox.style.display = 'block';

      // Si on ne maintient pas Ctrl, désélectionner tous les blocs
      if (!e.ctrlKey) {
        clearSelection();
      }
    }
  });

  document.addEventListener('mousemove', (e) => {
    if (isDragging) {
      // Déplacement du canvas avec le bouton du milieu
      offsetX = e.clientX - startX;
      offsetY = e.clientY - startY;
      updateCanvasPosition();
    } else if (isSelecting && selectionBox) {
      // Mise à jour de la boîte de sélection
      const width = e.clientX - selectionStartX;
      const height = e.clientY - selectionStartY;

      // Calculer les coordonnées pour la boîte de sélection
      const left = width > 0 ? selectionStartX : e.clientX;
      const top = height > 0 ? selectionStartY : e.clientY;
      const absWidth = Math.abs(width);
      const absHeight = Math.abs(height);

      // Mettre à jour la boîte de sélection
      selectionBox.style.left = `${left}px`;
      selectionBox.style.top = `${top}px`;
      selectionBox.style.width = `${absWidth}px`;
      selectionBox.style.height = `${absHeight}px`;

      // Sélectionner les blocs qui sont dans la boîte de sélection
      selectBlocksInSelectionBox(left, top, absWidth, absHeight);
    }
  });

  document.addEventListener('mouseup', (e) => {
    if (isDragging && e.button === 1) {
      isDragging = false;
      canvasContainer.style.cursor = 'default';
    } else if (isSelecting && e.button === 0) {
      isSelecting = false;

      // Cacher la boîte de sélection
      if (selectionBox) {
        selectionBox.style.display = 'none';
      }
    }
  });

  // Gestion du zoom avec la molette
  canvasContainer.addEventListener('wheel', (e) => {
    e.preventDefault();
    const delta = e.deltaY > 0 ? 0.9 : 1.1;
    setZoom(scale * delta);
  });

  // Raccourcis clavier globaux
  document.addEventListener('keydown', (e) => {
    // Ctrl+A pour sélectionner tous les blocs
    if (e.ctrlKey && e.key === 'a') {
      e.preventDefault();
      selectAllBlocks();
    }

    // Ctrl+Shift+A pour sélectionner un bloc et tous ses descendants
    if (e.ctrlKey && e.shiftKey && e.key === 'a' && selectedBlocks.length === 1) {
      e.preventDefault();
      const blockId = selectedBlocks[0];
      selectBlockWithChildren(blockId);
    }

    // Échap pour désélectionner tous les blocs
    if (e.key === 'Escape') {
      clearSelection();
    }

    // Supprimer les blocs sélectionnés avec Delete ou Backspace
    if ((e.key === 'Delete' || e.key === 'Backspace') && selectedBlocks.length > 0) {
      e.preventDefault();
      if (confirm(`Êtes-vous sûr de vouloir supprimer ${selectedBlocks.length} bloc(s) ?`)) {
        deleteSelectedBlocks();
      }
    }
  });

  // Fonction pour sélectionner tous les blocs
  function selectAllBlocks() {
    clearSelection();
    document.querySelectorAll('.prompt-block, .image-block, .gif-block').forEach(blockElement => {
      const blockId = blockElement.dataset.blockId;
      selectedBlocks.push(blockId);
      blockElement.classList.add('selected');
    });
  }

  // Fonction pour sélectionner un bloc et tous ses descendants
  function selectBlockWithChildren(blockId) {
    // Trouver le bloc
    const blockElement = document.querySelector(`.prompt-block[data-block-id="${blockId}"], .image-block[data-block-id="${blockId}"], .gif-block[data-block-id="${blockId}"]`);
    if (!blockElement) return;

    // Sélectionner le bloc
    if (!selectedBlocks.includes(blockId)) {
      selectedBlocks.push(blockId);
      blockElement.classList.add('selected');
    }

    // Trouver tous les descendants
    const allChildren = [];

    // Fonction récursive pour trouver tous les descendants
    function findAllDescendants(currentId) {
      // Trouver tous les blocs enfants directs
      const directChildren = canvasData.blocks.filter(block => block.parentId === currentId);

      directChildren.forEach(child => {
        allChildren.push(child);
        // Trouver récursivement les descendants de ce bloc
        findAllDescendants(child.id);
      });
    }

    // Trouver tous les descendants
    findAllDescendants(blockId);

    // Sélectionner tous les descendants
    allChildren.forEach(child => {
      const childElement = document.querySelector(`.prompt-block[data-block-id="${child.id}"], .image-block[data-block-id="${child.id}"], .gif-block[data-block-id="${child.id}"]`);
      if (childElement && !selectedBlocks.includes(child.id)) {
        selectedBlocks.push(child.id);
        childElement.classList.add('selected');
      }
    });
  }

  // Fonction pour supprimer tous les blocs sélectionnés
  function deleteSelectedBlocks() {
    // Créer une copie du tableau pour éviter les problèmes lors de la suppression
    const blocksToDelete = [...selectedBlocks];
    blocksToDelete.forEach(blockId => {
      removeBlock(blockId);
    });
    selectedBlocks = [];
  }

  // Fonctions
  function initCanvas() {
    // Vérifier si on ouvre un canvas existant
    const urlParams = new URLSearchParams(window.location.search);
    canvasId = urlParams.get('id');

    if (canvasId) {
      loadCanvas(canvasId);
    } else {
      canvasId = generateUniqueId();
      canvasNameInput.value = canvasName;
    }

    resetView();
  }

  function resetView() {
    scale = 1;
    offsetX = 0;
    offsetY = 0;
    updateCanvasPosition();
  }

  function setZoom(newScale) {
    scale = Math.max(0.1, Math.min(3, newScale));
    updateCanvasPosition();
  }

  function updateCanvasPosition() {
    canvas.style.transform = `translate(${offsetX}px, ${offsetY}px) scale(${scale})`;
  }

  // Fonctions de gestion de la sélection
  function toggleBlockSelection(blockElement) {
    if (!blockElement) return;

    const blockId = blockElement.dataset.blockId;
    const index = selectedBlocks.indexOf(blockId);

    if (index === -1) {
      // Ajouter à la sélection
      selectedBlocks.push(blockId);
      blockElement.classList.add('selected');
    } else {
      // Retirer de la sélection
      selectedBlocks.splice(index, 1);
      blockElement.classList.remove('selected');
    }
  }

  function clearSelection() {
    // Désélectionner tous les blocs
    selectedBlocks.forEach(blockId => {
      const blockElement = document.querySelector(`.prompt-block[data-block-id="${blockId}"], .image-block[data-block-id="${blockId}"], .gif-block[data-block-id="${blockId}"]`);
      if (blockElement) {
        blockElement.classList.remove('selected');
      }
    });

    selectedBlocks = [];
  }

  function selectBlocksInSelectionBox(left, top, width, height) {
    // Convertir les coordonnées de la boîte de sélection en coordonnées du canvas
    const canvasRect = canvas.getBoundingClientRect();
    const selectionLeft = (left - canvasRect.left - offsetX) / scale;
    const selectionTop = (top - canvasRect.top - offsetY) / scale;
    const selectionRight = selectionLeft + width / scale;
    const selectionBottom = selectionTop + height / scale;

    // Vérifier chaque bloc
    document.querySelectorAll('.prompt-block, .image-block, .gif-block').forEach(blockElement => {
      const blockId = blockElement.dataset.blockId;
      const blockLeft = parseInt(blockElement.style.left);
      const blockTop = parseInt(blockElement.style.top);
      const blockRight = blockLeft + blockElement.offsetWidth;
      const blockBottom = blockTop + blockElement.offsetHeight;

      // Vérifier si le bloc est dans la boîte de sélection
      const isInSelectionBox =
        blockLeft < selectionRight &&
        blockRight > selectionLeft &&
        blockTop < selectionBottom &&
        blockBottom > selectionTop;

      // Mettre à jour la sélection
      if (isInSelectionBox) {
        if (!selectedBlocks.includes(blockId)) {
          selectedBlocks.push(blockId);
          blockElement.classList.add('selected');
        }
      } else {
        // Si le bloc n'est pas dans la sélection et qu'on n'utilise pas Ctrl, le désélectionner
        if (selectedBlocks.includes(blockId)) {
          const index = selectedBlocks.indexOf(blockId);
          selectedBlocks.splice(index, 1);
          blockElement.classList.remove('selected');
        }
      }
    });
  }

  function createPromptBlock(options = {}) {
    const blockId = options.id || `block-${nextBlockId++}`;
    const x = options.x || 100;
    const y = options.y || 100;
    const prompt = options.prompt || '';
    const response = options.response || '';
    const parentId = options.parentId || null;
    const context = options.context || null;

    // Cloner le template
    const blockNode = promptBlockTemplate.content.cloneNode(true);
    const blockElement = blockNode.querySelector('.prompt-block');

    // Configurer le bloc
    blockElement.dataset.blockId = blockId;
    blockElement.style.left = `${x}px`;
    blockElement.style.top = `${y}px`;

    // Si c'est un bloc branche, ajouter une classe spéciale
    if (parentId) {
      blockElement.classList.add('branch-block');
      // Ajouter l'indicateur de type "Branche"
      const typeIndicator = blockElement.querySelector('.block-type-indicator');
      if (typeIndicator) {
        typeIndicator.textContent = 'BRANCHE';
      }
    } else {
      blockElement.classList.add('root-block');
      // Ajouter l'indicateur de type "Racine"
      const typeIndicator = blockElement.querySelector('.block-type-indicator');
      if (typeIndicator) {
        typeIndicator.textContent = 'RACINE';
      }
    }

    const promptInput = blockElement.querySelector('.prompt-input');
    promptInput.value = prompt;

    // Mettre à jour le contexte des blocs branches lorsque le contenu du bloc racine change
    if (!parentId) { // Seulement pour les blocs racines
      // Utiliser un délai pour ne pas mettre à jour trop souvent pendant la saisie
      let inputTimeout;
      promptInput.addEventListener('input', () => {
        clearTimeout(inputTimeout);
        inputTimeout = setTimeout(() => {
          updateChildBlocksContext(blockId, promptInput.value);
        }, 300); // Délai de 300ms pour éviter trop de mises à jour pendant la saisie rapide
      });

      // Mettre à jour également lors de la perte de focus (pour être sûr)
      promptInput.addEventListener('blur', () => {
        updateChildBlocksContext(blockId, promptInput.value);
      });
    }

    // Si c'est un bloc branche avec contexte, ajouter un indicateur de contexte
    if (parentId && context) {
      const contextIndicator = document.createElement('div');
      contextIndicator.className = 'context-indicator';

      // Créer un aperçu du contenu du bloc parent
      const promptPreview = context.parentPrompt.substring(0, 50) + (context.parentPrompt.length > 50 ? '...' : '');
      contextIndicator.innerHTML = '<span class="context-icon">🔄</span> Bloc parent: ' + promptPreview;
      contextIndicator.title = `Contenu complet du bloc parent: "${context.parentPrompt}"`;

      // Stocker le contexte dans les attributs de données
      blockElement.dataset.parentPrompt = context.parentPrompt;
      blockElement.dataset.parentResponse = context.parentResponse;

      // Ajouter également le contenu du parent dans le titre du bloc
      const blockTitleText = blockElement.querySelector('.block-title-text');
      if (blockTitleText) {
        blockTitleText.textContent = 'Bloc Prompt - Parent: ' + promptPreview.substring(0, 20) + (promptPreview.length > 20 ? '...' : '');
      }

      // Insérer l'indicateur avant la zone de texte
      const promptContent = blockElement.querySelector('.prompt-block-content');
      promptContent.insertBefore(contextIndicator, promptContent.firstChild);
    }

    const responseContent = blockElement.querySelector('.response-content');
    responseContent.innerHTML = response;

    // Ajouter le bloc au canvas
    canvas.appendChild(blockElement);

    // Enregistrer les données du bloc
    canvasData.blocks.push({
      id: blockId,
      type: 'prompt',
      x,
      y,
      prompt,
      response,
      parentId,
      context
    });

    // Si c'est un bloc branche, créer une connexion avec le parent
    if (parentId) {
      createConnection(parentId, blockId);
    }

    // Configurer les événements du bloc
    setupBlockEvents(blockElement);

    // Configurer le bouton de génération d'image
    setupGenerateImageFromPrompt(blockElement);

    return blockElement;
  }

  function setupBlockEvents(blockElement) {
    const header = blockElement.querySelector('.prompt-block-header');
    const minimizeButton = blockElement.querySelector('.minimize-block');
    const closeButton = blockElement.querySelector('.close-block');
    const sendPromptButton = blockElement.querySelector('.send-prompt');
    const createBranchButton = blockElement.querySelector('.create-branch');
    const resizeHandle = blockElement.querySelector('.prompt-block-resize-handle');
    const blockId = blockElement.dataset.blockId;

    // Rendre le bloc déplaçable
    let isDraggingBlock = false;
    let blockStartX, blockStartY;

    header.addEventListener('mousedown', (e) => {
      // Si c'est un clic gauche
      if (e.button === 0) {
        // Si on maintient Ctrl, ajouter/retirer de la sélection sans démarrer le déplacement
        if (e.ctrlKey) {
          toggleBlockSelection(blockElement);
        } else {
          // Si le bloc n'est pas déjà sélectionné, le sélectionner et désélectionner les autres
          if (!blockElement.classList.contains('selected')) {
            clearSelection();
            toggleBlockSelection(blockElement);
          }

          // Démarrer le déplacement pour tous les blocs sélectionnés
          isDraggingBlock = true;
          blockStartX = e.clientX - parseInt(blockElement.style.left);
          blockStartY = e.clientY - parseInt(blockElement.style.top);
          activeBlock = blockElement;
        }

        // Mettre le bloc au premier plan
        canvas.querySelectorAll('.prompt-block, .image-block').forEach(block => {
          block.style.zIndex = '1';
        });
        blockElement.style.zIndex = '10';

        e.stopPropagation();
      }
    });

    document.addEventListener('mousemove', (e) => {
      if (!isDraggingBlock || activeBlock !== blockElement) return;

      const newX = e.clientX - blockStartX;
      const newY = e.clientY - blockStartY;
      const deltaX = newX - parseInt(blockElement.style.left);
      const deltaY = newY - parseInt(blockElement.style.top);

      // Déplacer le bloc actif
      blockElement.style.left = `${newX}px`;
      blockElement.style.top = `${newY}px`;

      // Déplacer tous les autres blocs sélectionnés
      if (selectedBlocks.length > 1) {
        selectedBlocks.forEach(selectedBlockId => {
          if (selectedBlockId !== blockId) {
            const selectedBlockElement = document.querySelector(`.prompt-block[data-block-id="${selectedBlockId}"], .image-block[data-block-id="${selectedBlockId}"]`);
            if (selectedBlockElement) {
              const currentX = parseInt(selectedBlockElement.style.left);
              const currentY = parseInt(selectedBlockElement.style.top);
              selectedBlockElement.style.left = `${currentX + deltaX}px`;
              selectedBlockElement.style.top = `${currentY + deltaY}px`;

              // Mettre à jour les connexions pour ce bloc
              updateConnections(selectedBlockId);
            }
          }
        });
      }

      // Mettre à jour les connexions pour le bloc actif
      updateConnections(blockId);

      e.stopPropagation();
    });

    document.addEventListener('mouseup', () => {
      if (isDraggingBlock && activeBlock === blockElement) {
        isDraggingBlock = false;
        activeBlock = null;

        // Mettre à jour les données de tous les blocs sélectionnés
        selectedBlocks.forEach(selectedBlockId => {
          const selectedBlockElement = document.querySelector(`.prompt-block[data-block-id="${selectedBlockId}"], .image-block[data-block-id="${selectedBlockId}"]`);
          if (selectedBlockElement) {
            const blockIndex = canvasData.blocks.findIndex(block => block.id === selectedBlockId);
            if (blockIndex !== -1) {
              const newX = parseInt(selectedBlockElement.style.left);
              const newY = parseInt(selectedBlockElement.style.top);
              const oldY = canvasData.blocks[blockIndex].y;
              const deltaY = newY - oldY;

              canvasData.blocks[blockIndex].x = newX;
              canvasData.blocks[blockIndex].y = newY;

              // Si c'est un bloc racine, mettre à jour la position Y de tous les blocs branches et images associés
              const isRootBlock = selectedBlockElement.classList.contains('root-block');

              if (isRootBlock) {
                // Ajouter un délai pour s'assurer que la position du bloc est mise à jour avant de déplacer les enfants
                setTimeout(() => {
                  updateChildrenVerticalPosition(selectedBlockId, deltaY);
                }, 10);
              }
            }
          }
        });
      }
    });

    // Rendre le bloc redimensionnable
    let isResizing = false;
    let originalWidth, originalHeight, startResizeX, startResizeY;

    resizeHandle.addEventListener('mousedown', (e) => {
      isResizing = true;
      originalWidth = blockElement.offsetWidth;
      originalHeight = blockElement.offsetHeight;
      startResizeX = e.clientX;
      startResizeY = e.clientY;
      e.stopPropagation();
    });

    document.addEventListener('mousemove', (e) => {
      if (!isResizing) return;

      const deltaX = e.clientX - startResizeX;
      const deltaY = e.clientY - startResizeY;

      blockElement.style.width = `${originalWidth + deltaX}px`;
      blockElement.style.height = `${originalHeight + deltaY}px`;

      e.stopPropagation();
    });

    document.addEventListener('mouseup', () => {
      isResizing = false;
    });

    // Minimiser le bloc
    minimizeButton.addEventListener('click', () => {
      const content = blockElement.querySelector('.prompt-block-content');
      content.style.display = content.style.display === 'none' ? 'flex' : 'none';
      minimizeButton.textContent = content.style.display === 'none' ? '□' : '_';
    });

    // Fermer le bloc
    closeButton.addEventListener('click', () => {
      if (confirm('Êtes-vous sûr de vouloir supprimer ce bloc ?')) {
        removeBlock(blockId);
      }
    });

    // Dupliquer le bloc et ses branches
    const duplicateButton = blockElement.querySelector('.duplicate-block');
    if (duplicateButton) {
      duplicateButton.addEventListener('click', () => {
        duplicateBlockWithBranches(blockId);
      });
    }

    // Fonction pour générer ou régénérer une réponse
    async function generateResponse(isRegenerating = false, contextType = 'both') {
      const promptInput = blockElement.querySelector('.prompt-input');
      const responseContainer = blockElement.querySelector('.response-content');
      const prompt = promptInput.value.trim();

      if (!prompt) {
        alert('Veuillez entrer un prompt.');
        return;
      }

      try {
        // Afficher l'indicateur de chargement
        responseContainer.innerHTML = '<div class="loading"></div>';

        // Vérifier si c'est un bloc branche avec contexte
        let fullPrompt = prompt;
        if (blockElement.classList.contains('branch-block') &&
            blockElement.dataset.parentPrompt &&
            blockElement.dataset.parentResponse) {

          // Construire un prompt qui inclut le contexte du parent selon le type demandé
          if (contextType === 'both') {
            fullPrompt = `Tu es un assistant créatif qui aide à développer des scènes pour un scénario.
Voici le contexte précédent:
Scène: ${blockElement.dataset.parentPrompt}

Maintenant, en te basant sur ce contexte, ${prompt}
Assure-toi que ta réponse est cohérente avec la scène décrite dans le contexte.`;
          } else if (contextType === 'prompt') {
            // Pour le bouton "Générer réponse basée sur la scène du parent"
            // Utiliser directement le contenu du bloc parent comme instruction
            fullPrompt = `${blockElement.dataset.parentPrompt}`;
          } else if (contextType === 'response') {
            // Pour le bouton "Générer réponse basée sur la réponse du parent"
            // Utiliser directement la réponse du bloc parent
            fullPrompt = `${stripHtml(blockElement.dataset.parentResponse)}`;
          }

          // Mettre à jour l'indicateur de contexte
          const contextIndicator = blockElement.querySelector('.context-indicator');
          if (contextIndicator) {
            contextIndicator.classList.add('context-active');
          }
        }

        // Envoyer la requête à l'API Gemini avec le prompt complet
        const response = await geminiAPI.generateContent(fullPrompt);

        // Afficher la réponse
        responseContainer.innerHTML = formatResponse(response);

        // Ajouter une animation pour indiquer la mise à jour si c'est une régénération
        if (isRegenerating) {
          responseContainer.classList.add('updating');
          setTimeout(() => {
            responseContainer.classList.remove('updating');
          }, 1000);
        }

        // Mettre à jour les données du bloc
        const blockIndex = canvasData.blocks.findIndex(block => block.id === blockId);
        if (blockIndex !== -1) {
          canvasData.blocks[blockIndex].prompt = prompt;
          canvasData.blocks[blockIndex].response = response;
          // Sauvegarder également le prompt complet pour référence
          canvasData.blocks[blockIndex].fullPrompt = fullPrompt;

          // Si c'est un bloc racine, mettre à jour le contexte des blocs branches
          if (!blockElement.classList.contains('branch-block')) {
            // Mettre à jour le contexte des blocs branches avec la nouvelle réponse
            updateChildBlocksResponseContext(blockId, response);
          }
        }

        // Vérifier s'il y a des blocs d'images liés à ce bloc de prompt
        const linkedImageBlocks = canvasData.blocks.filter(block =>
          block.type === 'image' &&
          block.linkedToResponseOf === blockId
        );

        // Mettre à jour les blocs d'images liés
        if (linkedImageBlocks.length > 0) {
          linkedImageBlocks.forEach(imageBlock => {
            const imageBlockElement = document.querySelector(`.image-block[data-block-id="${imageBlock.id}"]`);
            if (imageBlockElement) {
              const promptInput = imageBlockElement.querySelector('.image-prompt-input');
              const imageContainer = imageBlockElement.querySelector('.image-result');
              const modelSelector = imageBlockElement.querySelector('.image-model-selector');
              const selectedModel = modelSelector.value;

              // Mettre à jour le prompt avec la nouvelle réponse
              const newPrompt = stripHtml(response).substring(0, 500);
              promptInput.value = newPrompt;

              // Afficher l'indicateur de chargement
              imageContainer.innerHTML = '<div class="loading"></div>';

              // Générer une nouvelle image
              const imageUrl = pollinationsAPI.generateImage(newPrompt, selectedModel);

              // Créer l'élément image
              const img = new Image();
              img.alt = newPrompt;

              // Gérer le chargement de l'image
              img.onload = () => {
                // Afficher l'image
                imageContainer.innerHTML = '';
                imageContainer.appendChild(img);

                // Mettre à jour les données du bloc
                const blockIndex = canvasData.blocks.findIndex(block => block.id === imageBlock.id);
                if (blockIndex !== -1) {
                  canvasData.blocks[blockIndex].prompt = newPrompt;
                  canvasData.blocks[blockIndex].imageUrl = imageUrl;
                }
              };

              img.onerror = () => {
                imageContainer.innerHTML = '<div class="error">Erreur lors du chargement de l\'image</div>';
              };

              // Déclencher le chargement de l'image
              img.src = imageUrl;

              // Ajouter une animation pour indiquer la mise à jour
              imageBlockElement.classList.add('updating');
              setTimeout(() => {
                imageBlockElement.classList.remove('updating');
              }, 1000);
            }
          });
        }
      } catch (error) {
        responseContainer.innerHTML = `<div class="error">Erreur: ${error.message}</div>`;
      }
    }

    // Envoyer le prompt à Gemini (première génération)
    sendPromptButton.addEventListener('click', () => {
      generateResponse(false, 'both');
    });

    // Régénérer la réponse
    const regenerateButton = blockElement.querySelector('.regenerate-response');
    if (regenerateButton) {
      regenerateButton.addEventListener('click', () => {
        generateResponse(true, 'both');
      });
    }

    // Générer une réponse en utilisant uniquement le contexte du prompt parent
    const generateFromParentPromptButton = blockElement.querySelector('.generate-from-parent-prompt');
    if (generateFromParentPromptButton) {
      generateFromParentPromptButton.addEventListener('click', () => {
        if (blockElement.classList.contains('branch-block') &&
            blockElement.dataset.parentPrompt) {
          // Générer la réponse en utilisant le contexte du prompt parent
          // sans modifier le contenu du champ prompt
          generateResponseWithContext(false, 'prompt');
        } else {
          alert('Ce bloc n\'est pas une branche ou ne contient pas de contexte parent.');
        }
      });
    }

    // Générer une réponse en utilisant uniquement le contexte de la réponse parent
    const generateFromParentResponseButton = blockElement.querySelector('.generate-from-parent-response');
    if (generateFromParentResponseButton) {
      generateFromParentResponseButton.addEventListener('click', () => {
        if (blockElement.classList.contains('branch-block') &&
            blockElement.dataset.parentResponse) {
          // Générer la réponse en utilisant le contexte de la réponse parent
          // sans modifier le contenu du champ prompt
          generateResponseWithContext(false, 'response');
        } else {
          alert('Ce bloc n\'est pas une branche ou ne contient pas de contexte parent.');
        }
      });
    }

    // Fonction pour générer une réponse avec un contexte spécifique
    async function generateResponseWithContext(isRegenerating = false, contextType = 'both') {
      const promptInput = blockElement.querySelector('.prompt-input');
      const responseContainer = blockElement.querySelector('.response-content');
      const prompt = promptInput.value.trim();

      if (!prompt) {
        alert('Veuillez entrer un prompt.');
        return;
      }

      try {
        // Afficher l'indicateur de chargement
        responseContainer.innerHTML = '<div class="loading"></div>';

        // Construire le prompt complet en fonction du type de contexte
        let fullPrompt;
        if (contextType === 'prompt') {
          fullPrompt = `Tu es un assistant créatif qui aide à développer des scènes pour un scénario.
Voici la scène principale:
${blockElement.dataset.parentPrompt}

Maintenant, en te basant UNIQUEMENT sur cette scène principale, réponds à cette question: ${prompt}
Ta réponse doit être en parfaite adéquation avec la scène principale décrite ci-dessus.
N'invente pas de nouveaux éléments qui ne seraient pas cohérents avec cette scène.`;
        } else if (contextType === 'response') {
          fullPrompt = `Tu es un assistant créatif qui aide à développer des scènes pour un scénario.
Voici une réponse précédente:
${stripHtml(blockElement.dataset.parentResponse)}

Maintenant, en te basant sur cette réponse, réponds à cette question: ${prompt}
Assure-toi que ta réponse est cohérente avec les éléments mentionnés précédemment.`;
        }

        // Mettre à jour l'indicateur de contexte
        const contextIndicator = blockElement.querySelector('.context-indicator');
        if (contextIndicator) {
          contextIndicator.classList.add('context-active');
        }

        // Envoyer la requête à l'API Gemini avec le prompt complet
        const response = await geminiAPI.generateContent(fullPrompt);

        // Afficher la réponse
        responseContainer.innerHTML = formatResponse(response);

        // Ajouter une animation pour indiquer la mise à jour si c'est une régénération
        if (isRegenerating) {
          responseContainer.classList.add('updating');
          setTimeout(() => {
            responseContainer.classList.remove('updating');
          }, 1000);
        }

        // Mettre à jour les données du bloc
        const blockIndex = canvasData.blocks.findIndex(block => block.id === blockId);
        if (blockIndex !== -1) {
          canvasData.blocks[blockIndex].prompt = prompt;
          canvasData.blocks[blockIndex].response = response;
          // Sauvegarder également le prompt complet pour référence
          canvasData.blocks[blockIndex].fullPrompt = fullPrompt;

          // Si c'est un bloc racine, mettre à jour le contexte des blocs branches
          if (!blockElement.classList.contains('branch-block')) {
            // Mettre à jour le contexte des blocs branches avec la nouvelle réponse
            updateChildBlocksResponseContext(blockId, response);
          }
        }

        // Vérifier s'il y a des blocs d'images liés à ce bloc de prompt
        updateLinkedImageBlocks(blockId, response);
      } catch (error) {
        responseContainer.innerHTML = `<div class="error">Erreur: ${error.message}</div>`;
      }
    }

    // Fonction pour mettre à jour les blocs d'images liés
    function updateLinkedImageBlocks(blockId, response) {
      const linkedImageBlocks = canvasData.blocks.filter(block =>
        block.type === 'image' &&
        block.linkedToResponseOf === blockId
      );

      // Mettre à jour les blocs d'images liés
      if (linkedImageBlocks.length > 0) {
        linkedImageBlocks.forEach(imageBlock => {
          const imageBlockElement = document.querySelector(`.image-block[data-block-id="${imageBlock.id}"]`);
          if (imageBlockElement) {
            const promptInput = imageBlockElement.querySelector('.image-prompt-input');
            const imageContainer = imageBlockElement.querySelector('.image-result');
            const modelSelector = imageBlockElement.querySelector('.image-model-selector');
            const selectedModel = modelSelector.value;

            // Mettre à jour le prompt avec la nouvelle réponse
            const newPrompt = stripHtml(response).substring(0, 500);
            promptInput.value = newPrompt;

            // Afficher l'indicateur de chargement
            imageContainer.innerHTML = '<div class="loading"></div>';

            // Générer une nouvelle image
            const imageUrl = pollinationsAPI.generateImage(newPrompt, selectedModel);

            // Créer l'élément image
            const img = new Image();
            img.alt = newPrompt;

            // Gérer le chargement de l'image
            img.onload = () => {
              // Afficher l'image
              imageContainer.innerHTML = '';
              imageContainer.appendChild(img);

              // Mettre à jour les données du bloc
              const blockIndex = canvasData.blocks.findIndex(block => block.id === imageBlock.id);
              if (blockIndex !== -1) {
                canvasData.blocks[blockIndex].prompt = newPrompt;
                canvasData.blocks[blockIndex].imageUrl = imageUrl;
              }
            };

            img.onerror = () => {
              imageContainer.innerHTML = '<div class="error">Erreur lors du chargement de l\'image</div>';
            };

            // Déclencher le chargement de l'image
            img.src = imageUrl;

            // Ajouter une animation pour indiquer la mise à jour
            imageBlockElement.classList.add('updating');
            setTimeout(() => {
              imageBlockElement.classList.remove('updating');
            }, 1000);
          }
        });
      }
    }

    // Créer une branche
    createBranchButton.addEventListener('click', () => {
      const parentRect = blockElement.getBoundingClientRect();
      const x = parseInt(blockElement.style.left) + parentRect.width + 50;
      const y = parseInt(blockElement.style.top);

      // Récupérer le prompt et la réponse du parent pour le contexte
      const promptInput = blockElement.querySelector('.prompt-input');
      const responseContent = blockElement.querySelector('.response-content');
      const parentPrompt = promptInput.value.trim();
      const parentResponse = responseContent.innerHTML;

      // Créer un bloc branche avec le contexte du parent
      createPromptBlock({
        x,
        y,
        parentId: blockId,
        context: {
          parentPrompt,
          parentResponse
        }
      });
    });
  }

  function removeBlock(blockId) {
    // Supprimer l'élément du DOM
    const blockElement = document.querySelector(`.prompt-block[data-block-id="${blockId}"], .image-block[data-block-id="${blockId}"], .gif-block[data-block-id="${blockId}"]`);
    if (blockElement) {
      blockElement.remove();
    }

    // Supprimer les connexions associées
    const connectionsToRemove = document.querySelectorAll(`.block-connection[data-source="${blockId}"], .block-connection[data-target="${blockId}"]`);
    connectionsToRemove.forEach(conn => conn.remove());

    // Mettre à jour les données
    canvasData.blocks = canvasData.blocks.filter(block => block.id !== blockId);
    canvasData.connections = canvasData.connections.filter(
      conn => conn.sourceId !== blockId && conn.targetId !== blockId
    );

    // Supprimer récursivement les blocs enfants
    const childBlocks = canvasData.blocks.filter(block => block.parentId === blockId);
    childBlocks.forEach(child => {
      removeBlock(child.id);
    });
  }

  function createConnection(sourceId, targetId) {
    // Ajouter aux données
    canvasData.connections.push({
      sourceId,
      targetId
    });

    // Créer l'élément de connexion
    drawConnection(sourceId, targetId);
  }

  function drawConnection(sourceId, targetId) {
    const sourceBlock = document.querySelector(`[data-block-id="${sourceId}"]`);
    const targetBlock = document.querySelector(`[data-block-id="${targetId}"]`);

    if (!sourceBlock || !targetBlock) return;

    // Supprimer la connexion existante si elle existe
    const existingConnection = document.querySelector(`.block-connection[data-source="${sourceId}"][data-target="${targetId}"]`);
    if (existingConnection) {
      existingConnection.remove();
    }

    // Calculer les positions
    const sourceRect = sourceBlock.getBoundingClientRect();
    const targetRect = targetBlock.getBoundingClientRect();

    const sourceX = parseInt(sourceBlock.style.left) + sourceRect.width;
    const sourceY = parseInt(sourceBlock.style.top) + sourceRect.height / 2;
    const targetX = parseInt(targetBlock.style.left);
    const targetY = parseInt(targetBlock.style.top) + targetRect.height / 2;

    // Créer l'élément SVG
    const svgNS = "http://www.w3.org/2000/svg";
    const svg = document.createElementNS(svgNS, "svg");
    svg.classList.add('block-connection');
    svg.setAttribute('data-source', sourceId);
    svg.setAttribute('data-target', targetId);

    // Positionner le SVG
    const left = Math.min(sourceX, targetX);
    const top = Math.min(sourceY, targetY);
    const width = Math.abs(targetX - sourceX);
    const height = Math.abs(targetY - sourceY);

    svg.style.left = `${left}px`;
    svg.style.top = `${top}px`;
    svg.style.width = `${width}px`;
    svg.style.height = `${height}px`;

    // Créer la ligne
    const path = document.createElementNS(svgNS, "path");

    // Calculer les points de contrôle pour une courbe
    const startX = sourceX - left;
    const startY = sourceY - top;
    const endX = targetX - left;
    const endY = targetY - top;
    const controlX = (startX + endX) / 2;

    // Définir le chemin
    path.setAttribute("d", `M${startX},${startY} C${controlX},${startY} ${controlX},${endY} ${endX},${endY}`);
    path.setAttribute("fill", "none");
    path.setAttribute("stroke", "#3498db");
    path.setAttribute("stroke-width", "3");
    path.setAttribute("stroke-linecap", "round");
    path.setAttribute("filter", "drop-shadow(0 0 2px rgba(52, 152, 219, 0.7))");

    // Ajouter une animation pour rendre la ligne plus visible
    const animate = document.createElementNS(svgNS, "animate");
    animate.setAttribute("attributeName", "stroke-opacity");
    animate.setAttribute("values", "0.7;1;0.7");
    animate.setAttribute("dur", "2s");
    animate.setAttribute("repeatCount", "indefinite");
    path.appendChild(animate);

    svg.appendChild(path);
    canvas.appendChild(svg);
  }

  function updateConnections(blockId) {
    // Mettre à jour toutes les connexions impliquant ce bloc
    const connections = canvasData.connections.filter(
      conn => conn.sourceId === blockId || conn.targetId === blockId
    );

    connections.forEach(conn => {
      drawConnection(conn.sourceId, conn.targetId);
    });
  }

  function formatResponse(text) {
    // Convertir les sauts de ligne en balises <br>
    return text.replace(/\n/g, '<br>');
  }

  function stripHtml(html) {
    // Créer un élément temporaire
    const temp = document.createElement('div');
    temp.innerHTML = html;
    // Récupérer le texte sans HTML
    return temp.textContent || temp.innerText || '';
  }

  // Fonction pour mettre à jour le contexte des blocs branches lorsque le contenu du bloc parent change
  function updateChildBlocksContext(parentId, newParentPrompt) {
    // Trouver tous les blocs branches qui ont ce bloc comme parent
    const childBlocks = canvasData.blocks.filter(block => block.parentId === parentId);

    childBlocks.forEach(childBlock => {
      // Mettre à jour le contexte dans les données du canvas
      if (childBlock.context) {
        childBlock.context.parentPrompt = newParentPrompt;
      } else if (childBlock.parentId === parentId) {
        // Si le contexte n'existe pas, le créer
        childBlock.context = {
          parentPrompt: newParentPrompt,
          parentResponse: ''
        };
      }

      // Mettre à jour l'élément DOM
      const childElement = document.querySelector(`.prompt-block[data-block-id="${childBlock.id}"]`);
      if (childElement) {
        // Mettre à jour l'attribut de données
        childElement.dataset.parentPrompt = newParentPrompt;

        // Mettre à jour l'indicateur de contexte
        const contextIndicator = childElement.querySelector('.context-indicator');
        if (contextIndicator) {
          const promptPreview = newParentPrompt.substring(0, 50) + (newParentPrompt.length > 50 ? '...' : '');
          contextIndicator.innerHTML = '<span class="context-icon">🔄</span> Bloc parent: ' + promptPreview;
          contextIndicator.title = `Contenu complet du bloc parent: "${newParentPrompt}"`;
        } else {
          // Si l'indicateur n'existe pas, le créer
          const newContextIndicator = document.createElement('div');
          newContextIndicator.className = 'context-indicator';
          const promptPreview = newParentPrompt.substring(0, 50) + (newParentPrompt.length > 50 ? '...' : '');
          newContextIndicator.innerHTML = '<span class="context-icon">🔄</span> Bloc parent: ' + promptPreview;
          newContextIndicator.title = `Contenu complet du bloc parent: "${newParentPrompt}"`;

          // Insérer l'indicateur avant la zone de texte
          const promptContent = childElement.querySelector('.prompt-block-content');
          if (promptContent && promptContent.firstChild) {
            promptContent.insertBefore(newContextIndicator, promptContent.firstChild);
          }
        }

        // Mettre à jour le titre du bloc
        const blockTitleText = childElement.querySelector('.block-title-text');
        if (blockTitleText) {
          const promptPreview = newParentPrompt.substring(0, 20) + (newParentPrompt.length > 20 ? '...' : '');
          blockTitleText.textContent = 'Bloc Prompt - Parent: ' + promptPreview;
        }

        // Mettre à jour également le bloc parent dans l'en-tête
        const parentInfoElement = childElement.querySelector('.parent-info');
        if (parentInfoElement) {
          const promptPreview = newParentPrompt.substring(0, 50) + (newParentPrompt.length > 50 ? '...' : '');
          parentInfoElement.innerHTML = 'Bloc parent: ' + promptPreview;
          parentInfoElement.title = `Contenu complet du bloc parent: "${newParentPrompt}"`;
        }
      }
    });
  }

  // Fonction pour mettre à jour le contexte de réponse des blocs branches
  function updateChildBlocksResponseContext(parentId, newParentResponse) {
    // Trouver tous les blocs branches qui ont ce bloc comme parent
    const childBlocks = canvasData.blocks.filter(block => block.parentId === parentId);

    childBlocks.forEach(childBlock => {
      // Mettre à jour le contexte dans les données du canvas
      if (childBlock.context) {
        childBlock.context.parentResponse = newParentResponse;
      } else if (childBlock.parentId === parentId) {
        // Si le contexte n'existe pas, le créer
        childBlock.context = {
          parentPrompt: '',
          parentResponse: newParentResponse
        };
      }

      // Mettre à jour l'élément DOM
      const childElement = document.querySelector(`.prompt-block[data-block-id="${childBlock.id}"]`);
      if (childElement) {
        // Mettre à jour l'attribut de données
        childElement.dataset.parentResponse = newParentResponse;
      }
    });
  }

  function saveCanvas() {
    const name = canvasNameInput.value.trim() || 'Canvas sans nom';

    // Mettre à jour les données du canvas
    canvasName = name;

    // Nettoyer les données avant la sauvegarde
    cleanCanvasData();

    // Sauvegarder dans le stockage local
    chrome.storage.local.get(['savedCanvases'], (result) => {
      const savedCanvases = result.savedCanvases || [];

      // Vérifier si le canvas existe déjà
      const existingIndex = savedCanvases.findIndex(canvas => canvas.id === canvasId);

      if (existingIndex !== -1) {
        // Mettre à jour le canvas existant
        savedCanvases[existingIndex] = {
          id: canvasId,
          name: canvasName,
          data: JSON.parse(JSON.stringify(canvasData)), // Créer une copie profonde pour éviter les références
          lastModified: new Date().toISOString()
        };
      } else {
        // Ajouter un nouveau canvas
        savedCanvases.push({
          id: canvasId,
          name: canvasName,
          data: JSON.parse(JSON.stringify(canvasData)), // Créer une copie profonde pour éviter les références
          lastModified: new Date().toISOString()
        });
      }

      chrome.storage.local.set({ savedCanvases }, () => {
        alert(`Canvas "${canvasName}" sauvegardé avec succès.`);
      });
    });
  }

  function cleanCanvasData() {
    // Récupérer tous les blocs actuellement présents dans le DOM
    const domBlockIds = Array.from(document.querySelectorAll('.prompt-block, .image-block, .gif-block'))
      .map(block => block.dataset.blockId);

    // Filtrer les blocs qui existent réellement dans le DOM
    canvasData.blocks = canvasData.blocks.filter(block => domBlockIds.includes(block.id));

    // Filtrer les connexions pour ne garder que celles entre des blocs existants
    canvasData.connections = canvasData.connections.filter(conn =>
      domBlockIds.includes(conn.sourceId) && domBlockIds.includes(conn.targetId)
    );

    // Vérifier et corriger les liens entre les blocs d'images et les blocs de prompts
    canvasData.blocks.forEach(block => {
      if (block.type === 'image' && block.linkedToResponseOf) {
        // Vérifier si le bloc de prompt lié existe toujours
        const linkedPromptExists = canvasData.blocks.some(b => b.id === block.linkedToResponseOf);
        if (!linkedPromptExists) {
          // Si le bloc lié n'existe plus, supprimer la référence
          block.linkedToResponseOf = null;
        }
      }
    });
  }

  function loadCanvas(id) {
    chrome.storage.local.get(['savedCanvases'], (result) => {
      const savedCanvases = result.savedCanvases || [];
      const canvas = savedCanvases.find(canvas => canvas.id === id);

      if (!canvas) {
        alert('Canvas introuvable.');
        return;
      }

      // Charger les données du canvas
      canvasName = canvas.name;
      // Créer une copie profonde pour éviter les problèmes de référence
      canvasData = JSON.parse(JSON.stringify(canvas.data));
      canvasNameInput.value = canvasName;

      // Effacer le canvas actuel
      document.querySelectorAll('.prompt-block, .image-block, .gif-block, .block-connection').forEach(el => el.remove());

      // Vérifier et nettoyer les données avant de recréer les blocs
      // Supprimer les doublons potentiels
      const uniqueBlockIds = new Set();
      canvasData.blocks = canvasData.blocks.filter(block => {
        if (uniqueBlockIds.has(block.id)) {
          return false; // Supprimer les doublons
        }
        uniqueBlockIds.add(block.id);
        return true;
      });

      // Recréer les blocs
      // D'abord les blocs de prompts, puis les blocs d'images et GIF pour assurer que les liens sont corrects
      const promptBlocks = canvasData.blocks.filter(block => block.type !== 'image' && block.type !== 'gif');
      const imageBlocks = canvasData.blocks.filter(block => block.type === 'image');
      const gifBlocks = canvasData.blocks.filter(block => block.type === 'gif');

      // Recréer les blocs de prompts
      promptBlocks.forEach(blockData => {
        createPromptBlock({
          id: blockData.id,
          x: blockData.x,
          y: blockData.y,
          prompt: blockData.prompt,
          response: blockData.response,
          parentId: blockData.parentId,
          context: blockData.context
        });
      });

      // Recréer les blocs d'images
      imageBlocks.forEach(blockData => {
        createImageBlock({
          id: blockData.id,
          x: blockData.x,
          y: blockData.y,
          prompt: blockData.prompt,
          imageUrl: blockData.imageUrl,
          parentId: blockData.parentId,
          model: blockData.model,
          linkedToResponseOf: blockData.linkedToResponseOf
        });
      });

      // Recréer les blocs GIF
      gifBlocks.forEach(blockData => {
        createGifBlock({
          id: blockData.id,
          x: blockData.x,
          y: blockData.y,
          sourceImages: blockData.sourceImages || [],
          gifUrl: blockData.gifUrl,
          delay: blockData.delay || 500,
          fitMode: blockData.fitMode || 'contain'
        });
      });

      // Recréer les connexions
      canvasData.connections.forEach(conn => {
        drawConnection(conn.sourceId, conn.targetId);
      });

      // Mettre à jour le compteur de blocs
      const blockIds = canvasData.blocks.map(block => {
        if (block.id.startsWith('image-')) {
          const match = block.id.match(/image-(\d+)/);
          return match ? parseInt(match[1]) : 0;
        } else if (block.id.startsWith('gif-')) {
          const match = block.id.match(/gif-(\d+)/);
          return match ? parseInt(match[1]) : 0;
        } else {
          const match = block.id.match(/block-(\d+)/);
          return match ? parseInt(match[1]) : 0;
        }
      });

      nextBlockId = blockIds.length > 0 ? Math.max(...blockIds) + 1 : 1;

      // Mettre à jour les contextes des blocs branches
      // Cela garantit que les indicateurs de contexte affichent les bonnes informations
      setTimeout(() => {
        // Trouver tous les blocs racines
        const rootBlocks = canvasData.blocks.filter(block => !block.parentId && block.type !== 'image');

        // Pour chaque bloc racine, mettre à jour le contexte de ses blocs branches
        rootBlocks.forEach(rootBlock => {
          const rootElement = document.querySelector(`.prompt-block[data-block-id="${rootBlock.id}"]`);
          if (rootElement) {
            const promptInput = rootElement.querySelector('.prompt-input');
            if (promptInput) {
              updateChildBlocksContext(rootBlock.id, promptInput.value);
            }

            const responseContent = rootElement.querySelector('.response-content');
            if (responseContent) {
              updateChildBlocksResponseContext(rootBlock.id, responseContent.innerHTML);
            }
          }
        });
      }, 200);

      // Nettoyer une dernière fois pour s'assurer que tout est cohérent
      cleanCanvasData();
    });
  }

  // Fonction pour créer un bloc GIF animé
  function createGifBlock(options = {}) {
    const blockId = options.id || `gif-${nextBlockId++}`;
    const x = options.x || 100;
    const y = options.y || 100;
    const sourceImages = options.sourceImages || [];
    const gifUrl = options.gifUrl || '';
    const delay = options.delay || 500;
    const fitMode = options.fitMode || 'contain';

    // Cloner le template
    const blockNode = document.getElementById('gif-block-template').content.cloneNode(true);
    const blockElement = blockNode.querySelector('.gif-block');

    // Configurer le bloc
    blockElement.dataset.blockId = blockId;
    blockElement.style.left = `${x}px`;
    blockElement.style.top = `${y}px`;

    // Mettre à jour le compteur d'images sources
    const sourceCountElement = blockElement.querySelector('.source-count');
    sourceCountElement.textContent = sourceImages.length;

    // Configurer le délai
    const delayInput = blockElement.querySelector('.gif-delay');
    delayInput.value = delay;

    // Configurer le mode d'ajustement
    const fitModeSelect = blockElement.querySelector('.gif-fit-mode');
    fitModeSelect.value = fitMode;

    // Afficher l'image GIF si disponible
    const gifResult = blockElement.querySelector('.gif-result');
    if (gifUrl) {
      gifResult.innerHTML = `<img src="${gifUrl}" alt="GIF animé" style="object-fit: ${fitMode};" />`;
    }

    // Afficher les images sources
    const sourceImagesList = blockElement.querySelector('.source-images-list');
    sourceImages.forEach(image => {
      const imageItem = document.createElement('div');
      imageItem.className = 'source-image-item';
      imageItem.dataset.imageUrl = image.url;
      imageItem.innerHTML = `
        <img src="${image.url}" alt="${image.prompt || 'Image source'}" />
        <div class="source-image-remove" title="Supprimer cette image">×</div>
      `;
      sourceImagesList.appendChild(imageItem);
    });

    // Ajouter le bloc au canvas
    canvas.appendChild(blockElement);

    // Enregistrer les données du bloc
    canvasData.blocks.push({
      id: blockId,
      type: 'gif',
      x,
      y,
      sourceImages,
      gifUrl,
      delay,
      fitMode
    });

    // Configurer les événements du bloc
    setupGifBlockEvents(blockElement);

    return blockElement;
  }

  function createImageBlock(options = {}) {
    const blockId = options.id || `image-${nextBlockId++}`;
    let x = options.x || 100;
    let y = options.y || 100;
    const prompt = options.prompt || '';
    const imageUrl = options.imageUrl || '';
    const parentId = options.parentId || null;
    const model = options.model || pollinationsAPI.getModel();
    const linkedToResponseOf = options.linkedToResponseOf || null;
    const aspectRatio = options.aspectRatio || '1:1';

    // Si un parent est spécifié et que les coordonnées ne sont pas explicitement fournies,
    // positionner le bloc d'image à côté du parent
    if (parentId && !options.hasOwnProperty('x')) {
      const parentElement = document.querySelector(`[data-block-id="${parentId}"]`);
      if (parentElement) {
        x = parseInt(parentElement.style.left) + parentElement.offsetWidth + 20;
        y = parseInt(parentElement.style.top); // Même hauteur que le parent
      }
    }

    // Cloner le template
    const blockNode = imageBlockTemplate.content.cloneNode(true);
    const blockElement = blockNode.querySelector('.image-block');

    // Configurer le bloc
    blockElement.dataset.blockId = blockId;
    blockElement.style.left = `${x}px`;
    blockElement.style.top = `${y}px`;

    // Si c'est un bloc branche, ajouter une classe spéciale
    if (parentId) {
      blockElement.classList.add('branch-block');
      // Ajouter l'indicateur de type "Branche"
      const typeIndicator = blockElement.querySelector('.block-type-indicator');
      if (typeIndicator) {
        typeIndicator.textContent = 'BRANCHE';
      }
    } else {
      blockElement.classList.add('root-block');
      // Ajouter l'indicateur de type "Racine"
      const typeIndicator = blockElement.querySelector('.block-type-indicator');
      if (typeIndicator) {
        typeIndicator.textContent = 'RACINE';
      }
    }

    // Si c'est un bloc lié à une réponse, ajouter une classe spéciale
    if (linkedToResponseOf) {
      blockElement.classList.add('response-linked-block');
      blockElement.dataset.linkedToResponseOf = linkedToResponseOf;

      // Ajouter un indicateur visuel
      const linkIndicator = document.createElement('div');
      linkIndicator.className = 'link-indicator';
      linkIndicator.innerHTML = '<span class="link-icon">🔄</span> Lié à la réponse';
      linkIndicator.title = 'Ce bloc est lié à la réponse d\'un bloc prompt et sera mis à jour automatiquement';

      // Insérer l'indicateur avant la zone de texte
      const imageContent = blockElement.querySelector('.image-block-content');
      imageContent.insertBefore(linkIndicator, imageContent.firstChild);

      // Mettre à jour le titre du bloc
      const blockTitleText = blockElement.querySelector('.block-title-text');
      if (blockTitleText) {
        blockTitleText.textContent = 'Bloc Image IA - Lié à réponse';
      }
    }

    // Si c'est un bloc branche, mettre à jour le titre
    if (parentId) {
      // Mettre à jour le titre du bloc
      const blockTitleText = blockElement.querySelector('.block-title-text');
      if (blockTitleText && !linkedToResponseOf) {
        blockTitleText.textContent = 'Bloc Image IA - Variante';
      }
    }

    const promptInput = blockElement.querySelector('.image-prompt-input');
    promptInput.value = prompt;

    const modelSelector = blockElement.querySelector('.image-model-selector');
    modelSelector.value = model;

    const imageResult = blockElement.querySelector('.image-result');
    if (imageUrl) {
      imageResult.innerHTML = `<img src="${imageUrl}" alt="${prompt}" />`;
    }

    // Ajouter le bloc au canvas
    canvas.appendChild(blockElement);

    // Enregistrer les données du bloc
    canvasData.blocks.push({
      id: blockId,
      type: 'image',
      x,
      y,
      prompt,
      imageUrl,
      parentId,
      model,
      linkedToResponseOf
    });

    // Si c'est un bloc branche, créer une connexion avec le parent
    if (parentId) {
      createConnection(parentId, blockId);
    }

    // Configurer les événements du bloc
    setupImageBlockEvents(blockElement);

    return blockElement;
  }

  function setupImageBlockEvents(blockElement) {
    const header = blockElement.querySelector('.image-block-header');
    const minimizeButton = blockElement.querySelector('.minimize-block');
    const closeButton = blockElement.querySelector('.close-block');
    const generateImageButton = blockElement.querySelector('.generate-image-btn');
    const createBranchButton = blockElement.querySelector('.create-image-branch');
    const refreshModelsButton = blockElement.querySelector('.refresh-models');
    const modelSelector = blockElement.querySelector('.image-model-selector');
    const resizeHandle = blockElement.querySelector('.image-block-resize-handle');
    const blockId = blockElement.dataset.blockId;

    // Rendre le bloc déplaçable
    let isDraggingBlock = false;
    let blockStartX, blockStartY;

    header.addEventListener('mousedown', (e) => {
      // Si c'est un clic gauche
      if (e.button === 0) {
        // Si on maintient Ctrl, ajouter/retirer de la sélection sans démarrer le déplacement
        if (e.ctrlKey) {
          toggleBlockSelection(blockElement);
        } else {
          // Si le bloc n'est pas déjà sélectionné, le sélectionner et désélectionner les autres
          if (!blockElement.classList.contains('selected')) {
            clearSelection();
            toggleBlockSelection(blockElement);
          }

          // Démarrer le déplacement pour tous les blocs sélectionnés
          isDraggingBlock = true;
          blockStartX = e.clientX - parseInt(blockElement.style.left);
          blockStartY = e.clientY - parseInt(blockElement.style.top);
          activeBlock = blockElement;
        }

        // Mettre le bloc au premier plan
        canvas.querySelectorAll('.prompt-block, .image-block, .gif-block').forEach(block => {
          block.style.zIndex = '1';
        });
        blockElement.style.zIndex = '10';

        e.stopPropagation();
      }
    });

    document.addEventListener('mousemove', (e) => {
      if (!isDraggingBlock || activeBlock !== blockElement) return;

      const newX = e.clientX - blockStartX;
      const newY = e.clientY - blockStartY;
      const deltaX = newX - parseInt(blockElement.style.left);
      const deltaY = newY - parseInt(blockElement.style.top);

      // Déplacer le bloc actif
      blockElement.style.left = `${newX}px`;
      blockElement.style.top = `${newY}px`;

      // Déplacer tous les autres blocs sélectionnés
      if (selectedBlocks.length > 1) {
        selectedBlocks.forEach(selectedBlockId => {
          if (selectedBlockId !== blockId) {
            const selectedBlockElement = document.querySelector(`.prompt-block[data-block-id="${selectedBlockId}"], .image-block[data-block-id="${selectedBlockId}"], .gif-block[data-block-id="${selectedBlockId}"]`);
            if (selectedBlockElement) {
              const currentX = parseInt(selectedBlockElement.style.left);
              const currentY = parseInt(selectedBlockElement.style.top);
              selectedBlockElement.style.left = `${currentX + deltaX}px`;
              selectedBlockElement.style.top = `${currentY + deltaY}px`;

              // Mettre à jour les connexions pour ce bloc
              updateConnections(selectedBlockId);
            }
          }
        });
      }

      // Mettre à jour les connexions pour le bloc actif
      updateConnections(blockId);

      e.stopPropagation();
    });

    document.addEventListener('mouseup', () => {
      if (isDraggingBlock && activeBlock === blockElement) {
        isDraggingBlock = false;
        activeBlock = null;

        // Mettre à jour les données de tous les blocs sélectionnés
        selectedBlocks.forEach(selectedBlockId => {
          const selectedBlockElement = document.querySelector(`.prompt-block[data-block-id="${selectedBlockId}"], .image-block[data-block-id="${selectedBlockId}"], .gif-block[data-block-id="${selectedBlockId}"]`);
          if (selectedBlockElement) {
            const blockIndex = canvasData.blocks.findIndex(block => block.id === selectedBlockId);
            if (blockIndex !== -1) {
              const newX = parseInt(selectedBlockElement.style.left);
              const newY = parseInt(selectedBlockElement.style.top);
              const oldY = canvasData.blocks[blockIndex].y;
              const deltaY = newY - oldY;

              canvasData.blocks[blockIndex].x = newX;
              canvasData.blocks[blockIndex].y = newY;

              // Si c'est un bloc racine, mettre à jour la position Y de tous les blocs branches et images associés
              const isRootBlock = selectedBlockElement.classList.contains('root-block');

              if (isRootBlock) {
                // Ajouter un délai pour s'assurer que la position du bloc est mise à jour avant de déplacer les enfants
                setTimeout(() => {
                  updateChildrenVerticalPosition(selectedBlockId, deltaY);
                }, 10);
              }
            }
          }
        });
      }
    });

    // Rendre le bloc redimensionnable
    let isResizing = false;
    let originalWidth, originalHeight, startResizeX, startResizeY;

    resizeHandle.addEventListener('mousedown', (e) => {
      isResizing = true;
      originalWidth = blockElement.offsetWidth;
      originalHeight = blockElement.offsetHeight;
      startResizeX = e.clientX;
      startResizeY = e.clientY;
      e.stopPropagation();
    });

    document.addEventListener('mousemove', (e) => {
      if (!isResizing) return;

      const deltaX = e.clientX - startResizeX;
      const deltaY = e.clientY - startResizeY;

      blockElement.style.width = `${originalWidth + deltaX}px`;
      blockElement.style.height = `${originalHeight + deltaY}px`;

      e.stopPropagation();
    });

    document.addEventListener('mouseup', () => {
      isResizing = false;
    });

    // Minimiser le bloc
    minimizeButton.addEventListener('click', () => {
      const content = blockElement.querySelector('.image-block-content');
      content.style.display = content.style.display === 'none' ? 'flex' : 'none';
      minimizeButton.textContent = content.style.display === 'none' ? '□' : '_';
    });

    // Fermer le bloc
    closeButton.addEventListener('click', () => {
      if (confirm('Êtes-vous sûr de vouloir supprimer ce bloc ?')) {
        removeBlock(blockId);
      }
    });

    // Dupliquer le bloc
    const duplicateButton = blockElement.querySelector('.duplicate-block');
    if (duplicateButton) {
      duplicateButton.addEventListener('click', () => {
        duplicateImageBlock(blockId);
      });
    }

    // Changer de modèle
    modelSelector.addEventListener('change', () => {
      const selectedModel = modelSelector.value;
      pollinationsAPI.setModel(selectedModel);

      // Mettre à jour les données du bloc
      const blockIndex = canvasData.blocks.findIndex(block => block.id === blockId);
      if (blockIndex !== -1) {
        canvasData.blocks[blockIndex].model = selectedModel;
      }
    });

    // Rafraîchir la liste des modèles
    refreshModelsButton.addEventListener('click', async () => {
      try {
        refreshModelsButton.disabled = true;
        refreshModelsButton.textContent = '⌛';

        const models = await pollinationsAPI.fetchModels();

        // Mettre à jour le sélecteur de modèles
        modelSelector.innerHTML = '';
        models.forEach(model => {
          const option = document.createElement('option');
          option.value = model.id;
          option.textContent = model.name;
          modelSelector.appendChild(option);
        });

        refreshModelsButton.textContent = '⟳';
      } catch (error) {
        console.error('Erreur lors du rafraîchissement des modèles:', error);
        alert('Erreur lors du rafraîchissement des modèles.');
      } finally {
        refreshModelsButton.disabled = false;
      }
    });

    // Générer une image
    generateImageButton.addEventListener('click', () => {
      const promptInput = blockElement.querySelector('.image-prompt-input');
      const imageContainer = blockElement.querySelector('.image-result');
      const prompt = promptInput.value.trim();

      if (!prompt) {
        alert('Veuillez entrer une description pour l\'image.');
        return;
      }

      try {
        // Afficher l'indicateur de chargement
        imageContainer.innerHTML = '<div class="loading"></div>';

        // Générer l'URL de l'image
        const selectedModel = modelSelector.value;
        const imageUrl = pollinationsAPI.generateImage(prompt, selectedModel);

        // Créer l'élément image
        const img = new Image();
        img.alt = prompt;

        // Gérer le chargement de l'image
        img.onload = () => {
          // Afficher l'image
          imageContainer.innerHTML = '';
          imageContainer.appendChild(img);

          // Mettre à jour les données du bloc
          const blockIndex = canvasData.blocks.findIndex(block => block.id === blockId);
          if (blockIndex !== -1) {
            canvasData.blocks[blockIndex].prompt = prompt;
            canvasData.blocks[blockIndex].imageUrl = imageUrl;
            canvasData.blocks[blockIndex].model = selectedModel;
          }
        };

        img.onerror = () => {
          imageContainer.innerHTML = '<div class="error">Erreur lors du chargement de l\'image</div>';
        };

        // Déclencher le chargement de l'image
        img.src = imageUrl;
      } catch (error) {
        imageContainer.innerHTML = `<div class="error">Erreur: ${error.message}</div>`;
      }
    });

    // Créer une variante
    createBranchButton.addEventListener('click', () => {
      const promptInput = blockElement.querySelector('.image-prompt-input');
      const prompt = promptInput.value.trim();

      if (!prompt) {
        alert('Veuillez d\'abord générer une image avant de créer une variante.');
        return;
      }

      const selectedModel = modelSelector.value;

      createImageBlock({
        prompt,
        parentId: blockId,
        model: selectedModel
      });
    });
  }

  // Configurer les événements pour les blocs GIF
  function setupGifBlockEvents(blockElement) {
    const header = blockElement.querySelector('.gif-block-header');
    const minimizeButton = blockElement.querySelector('.minimize-block');
    const closeButton = blockElement.querySelector('.close-block');
    const regenerateGifButton = blockElement.querySelector('.regenerate-gif-btn');
    const delayInput = blockElement.querySelector('.gif-delay');
    const resizeHandle = blockElement.querySelector('.gif-block-resize-handle');
    const blockId = blockElement.dataset.blockId;

    // Rendre le bloc déplaçable
    let isDraggingBlock = false;
    let blockStartX, blockStartY;

    header.addEventListener('mousedown', (e) => {
      // Si c'est un clic gauche
      if (e.button === 0) {
        // Si on maintient Ctrl, ajouter/retirer de la sélection sans démarrer le déplacement
        if (e.ctrlKey) {
          toggleBlockSelection(blockElement);
        } else {
          // Si le bloc n'est pas déjà sélectionné, le sélectionner et désélectionner les autres
          if (!blockElement.classList.contains('selected')) {
            clearSelection();
            toggleBlockSelection(blockElement);
          }

          // Démarrer le déplacement pour tous les blocs sélectionnés
          isDraggingBlock = true;
          blockStartX = e.clientX - parseInt(blockElement.style.left);
          blockStartY = e.clientY - parseInt(blockElement.style.top);
          activeBlock = blockElement;
        }

        // Mettre le bloc au premier plan
        canvas.querySelectorAll('.prompt-block, .image-block, .gif-block').forEach(block => {
          block.style.zIndex = '1';
        });
        blockElement.style.zIndex = '10';

        e.stopPropagation();
      }
    });

    document.addEventListener('mousemove', (e) => {
      if (!isDraggingBlock || activeBlock !== blockElement) return;

      const newX = e.clientX - blockStartX;
      const newY = e.clientY - blockStartY;
      const deltaX = newX - parseInt(blockElement.style.left);
      const deltaY = newY - parseInt(blockElement.style.top);

      // Déplacer le bloc actif
      blockElement.style.left = `${newX}px`;
      blockElement.style.top = `${newY}px`;

      // Déplacer tous les autres blocs sélectionnés
      if (selectedBlocks.length > 1) {
        selectedBlocks.forEach(selectedBlockId => {
          if (selectedBlockId !== blockId) {
            const selectedBlockElement = document.querySelector(`.prompt-block[data-block-id="${selectedBlockId}"], .image-block[data-block-id="${selectedBlockId}"], .gif-block[data-block-id="${selectedBlockId}"]`);
            if (selectedBlockElement) {
              const currentX = parseInt(selectedBlockElement.style.left);
              const currentY = parseInt(selectedBlockElement.style.top);
              selectedBlockElement.style.left = `${currentX + deltaX}px`;
              selectedBlockElement.style.top = `${currentY + deltaY}px`;
            }
          }
        });
      }

      e.stopPropagation();
    });

    document.addEventListener('mouseup', () => {
      if (isDraggingBlock && activeBlock === blockElement) {
        isDraggingBlock = false;
        activeBlock = null;

        // Mettre à jour les données du bloc
        const blockIndex = canvasData.blocks.findIndex(block => block.id === blockId);
        if (blockIndex !== -1) {
          canvasData.blocks[blockIndex].x = parseInt(blockElement.style.left);
          canvasData.blocks[blockIndex].y = parseInt(blockElement.style.top);
        }
      }
    });

    // Rendre le bloc redimensionnable
    let isResizing = false;
    let originalWidth, originalHeight, startResizeX, startResizeY;

    resizeHandle.addEventListener('mousedown', (e) => {
      isResizing = true;
      originalWidth = blockElement.offsetWidth;
      originalHeight = blockElement.offsetHeight;
      startResizeX = e.clientX;
      startResizeY = e.clientY;
      e.stopPropagation();
    });

    document.addEventListener('mousemove', (e) => {
      if (!isResizing) return;

      const deltaX = e.clientX - startResizeX;
      const deltaY = e.clientY - startResizeY;

      blockElement.style.width = `${originalWidth + deltaX}px`;
      blockElement.style.height = `${originalHeight + deltaY}px`;

      e.stopPropagation();
    });

    document.addEventListener('mouseup', () => {
      isResizing = false;
    });

    // Minimiser le bloc
    minimizeButton.addEventListener('click', () => {
      const content = blockElement.querySelector('.gif-block-content');
      content.style.display = content.style.display === 'none' ? 'flex' : 'none';
      minimizeButton.textContent = content.style.display === 'none' ? '□' : '_';
    });

    // Fermer le bloc
    closeButton.addEventListener('click', () => {
      if (confirm('Êtes-vous sûr de vouloir supprimer ce bloc ?')) {
        removeBlock(blockId);
      }
    });

    // Dupliquer le bloc
    const duplicateButton = blockElement.querySelector('.duplicate-block');
    if (duplicateButton) {
      duplicateButton.addEventListener('click', () => {
        duplicateGifBlock(blockId);
      });
    }

    // Récupérer le sélecteur de mode d'ajustement
    const fitModeSelect = blockElement.querySelector('.gif-fit-mode');

    // Régénérer le GIF
    regenerateGifButton.addEventListener('click', () => {
      const blockData = canvasData.blocks.find(block => block.id === blockId);
      if (blockData && blockData.sourceImages && blockData.sourceImages.length > 0) {
        // Mettre à jour le mode d'ajustement dans les données du bloc
        const blockIndex = canvasData.blocks.findIndex(block => block.id === blockId);
        if (blockIndex !== -1) {
          canvasData.blocks[blockIndex].fitMode = fitModeSelect.value;
        }

        // Générer le GIF avec les paramètres mis à jour
        generateGif(blockData.sourceImages, parseInt(delayInput.value), blockElement, fitModeSelect.value);
      } else {
        alert('Aucune image source disponible pour générer le GIF.');
      }
    });

    // Mettre à jour le délai
    delayInput.addEventListener('change', () => {
      const blockIndex = canvasData.blocks.findIndex(block => block.id === blockId);
      if (blockIndex !== -1) {
        canvasData.blocks[blockIndex].delay = parseInt(delayInput.value);
      }
    });

    // Mettre à jour le mode d'ajustement
    fitModeSelect.addEventListener('change', () => {
      const blockIndex = canvasData.blocks.findIndex(block => block.id === blockId);
      if (blockIndex !== -1) {
        canvasData.blocks[blockIndex].fitMode = fitModeSelect.value;

        // Mettre à jour l'affichage du GIF si disponible
        const gifResult = blockElement.querySelector('.gif-result img');
        if (gifResult) {
          gifResult.style.objectFit = fitModeSelect.value;
        }
      }
    });

    // Gérer la suppression d'images sources
    const sourceImagesList = blockElement.querySelector('.source-images-list');
    sourceImagesList.addEventListener('click', (e) => {
      if (e.target.classList.contains('source-image-remove')) {
        const imageItem = e.target.closest('.source-image-item');
        if (imageItem) {
          const imageUrl = imageItem.dataset.imageUrl;

          // Supprimer l'image des données du bloc
          const blockIndex = canvasData.blocks.findIndex(block => block.id === blockId);
          if (blockIndex !== -1) {
            canvasData.blocks[blockIndex].sourceImages = canvasData.blocks[blockIndex].sourceImages.filter(img => img.url !== imageUrl);

            // Mettre à jour le compteur
            const sourceCountElement = blockElement.querySelector('.source-count');
            sourceCountElement.textContent = canvasData.blocks[blockIndex].sourceImages.length;
          }

          // Supprimer l'élément du DOM
          imageItem.remove();
        }
      }
    });
  }

  // Fonction pour créer un GIF à partir des images sélectionnées
  function createGifFromSelectedImages() {
    // Vérifier s'il y a des blocs d'images sélectionnés
    const selectedImageBlocks = selectedBlocks
      .map(id => canvasData.blocks.find(block => block.id === id))
      .filter(block => block && block.type === 'image' && block.imageUrl);

    if (selectedImageBlocks.length < 2) {
      alert('Veuillez sélectionner au moins 2 blocs d\'images pour créer un GIF animé.');
      return;
    }

    // Récupérer les images des blocs sélectionnés
    const sourceImages = selectedImageBlocks.map(block => ({
      url: block.imageUrl,
      prompt: block.prompt
    }));

    // Calculer la position optimale pour le bloc GIF (en dessous des images sélectionnées)
    const selectedElements = selectedImageBlocks.map(block =>
      document.querySelector(`.image-block[data-block-id="${block.id}"]`)
    ).filter(el => el !== null);

    // Trouver les coordonnées des blocs sélectionnés
    let minX = Infinity;
    let maxX = -Infinity;
    let maxY = -Infinity;
    let maxHeight = 0;

    selectedElements.forEach(element => {
      const rect = element.getBoundingClientRect();
      const x = parseInt(element.style.left);
      const y = parseInt(element.style.top);

      minX = Math.min(minX, x);
      maxX = Math.max(maxX, x + rect.width);

      // Trouver le bloc le plus bas
      if (y > maxY) {
        maxY = y;
        maxHeight = rect.height;
      }
    });

    // Calculer la position centrale horizontalement et en dessous verticalement
    const centerX = minX + (maxX - minX) / 2 - 150; // 150 est la moitié de la largeur par défaut du bloc
    const belowY = maxY + maxHeight + 20; // 20px d'espace entre les blocs

    // Créer un nouveau bloc GIF
    const gifBlock = createGifBlock({
      x: Math.max(0, centerX),
      y: belowY,
      sourceImages,
      delay: 500,
      fitMode: 'contain'
    });

    // Générer le GIF
    generateGif(sourceImages, 500, gifBlock, 'contain');

    // Sélectionner le nouveau bloc GIF
    clearSelection();
    toggleBlockSelection(gifBlock);
  }

  // Fonction pour générer un GIF à partir d'images sources
  function generateGif(sourceImages, delay, blockElement, fitMode = 'contain') {
    if (sourceImages.length < 2) {
      alert('Il faut au moins 2 images pour créer un GIF animé.');
      return;
    }

    // Afficher un indicateur de chargement
    const gifResult = blockElement.querySelector('.gif-result');
    gifResult.innerHTML = '<div class="loading"></div>';

    // Précharger toutes les images pour déterminer les dimensions optimales du GIF
    let loadedImages = 0;
    const totalImages = sourceImages.length;
    const images = [];
    let maxWidth = 0;
    let maxHeight = 0;

    // Fonction pour créer le GIF une fois toutes les images chargées
    function createGifFromImages(images, maxWidth, maxHeight) {
      // Créer un nouvel objet GIF avec les dimensions maximales
      const gif = new GIF({
        workers: 2,
        quality: 10,
        workerScript: 'gif.worker.js',
        width: maxWidth,
        height: maxHeight,
        transparent: 'rgba(0,0,0,0)'
      });

      // Créer un canvas pour redimensionner les images tout en préservant le ratio d'aspect
      const canvas = document.createElement('canvas');
      canvas.width = maxWidth;
      canvas.height = maxHeight;
      const ctx = canvas.getContext('2d');

      // Ajouter chaque image au GIF
      images.forEach(image => {
        // Effacer le canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // Calculer les dimensions pour préserver le ratio d'aspect
        let drawWidth, drawHeight, offsetX = 0, offsetY = 0;

        const imageRatio = image.width / image.height;
        const canvasRatio = canvas.width / canvas.height;

        if (imageRatio > canvasRatio) {
          // L'image est plus large que le canvas (proportionnellement)
          drawWidth = canvas.width;
          drawHeight = canvas.width / imageRatio;
          offsetY = (canvas.height - drawHeight) / 2;
        } else {
          // L'image est plus haute que le canvas (proportionnellement)
          drawHeight = canvas.height;
          drawWidth = canvas.height * imageRatio;
          offsetX = (canvas.width - drawWidth) / 2;
        }

        // Dessiner l'image centrée dans le canvas
        ctx.drawImage(image, offsetX, offsetY, drawWidth, drawHeight);

        // Ajouter le frame au GIF
        gif.addFrame(canvas, { delay: delay, copy: true });
      });

      // Rendre le GIF
      gif.on('finished', blob => {
        // Créer une URL pour le blob
        const gifUrl = URL.createObjectURL(blob);

        // Afficher le GIF avec le mode d'ajustement spécifié
        gifResult.innerHTML = `<img src="${gifUrl}" alt="GIF animé" style="object-fit: ${fitMode};" />`;

        // Mettre à jour les données du bloc
        const blockId = blockElement.dataset.blockId;
        const blockIndex = canvasData.blocks.findIndex(block => block.id === blockId);
        if (blockIndex !== -1) {
          canvasData.blocks[blockIndex].gifUrl = gifUrl;
          canvasData.blocks[blockIndex].fitMode = fitMode;
        }
      });

      gif.render();
    }

    // Charger toutes les images et déterminer les dimensions maximales
    sourceImages.forEach((sourceImage, index) => {
      const img = new Image();
      img.crossOrigin = 'Anonymous';

      img.onload = () => {
        images[index] = img;

        // Mettre à jour les dimensions maximales
        maxWidth = Math.max(maxWidth, img.width);
        maxHeight = Math.max(maxHeight, img.height);

        loadedImages++;

        // Une fois que toutes les images sont chargées, créer le GIF
        if (loadedImages === totalImages) {
          // Limiter les dimensions maximales pour éviter des GIFs trop grands
          if (maxWidth > 800) {
            const ratio = 800 / maxWidth;
            maxWidth = 800;
            maxHeight = Math.floor(maxHeight * ratio);
          }

          if (maxHeight > 800) {
            const ratio = 800 / maxHeight;
            maxHeight = 800;
            maxWidth = Math.floor(maxWidth * ratio);
          }

          // S'assurer que les dimensions sont au moins de 300x300
          maxWidth = Math.max(300, maxWidth);
          maxHeight = Math.max(300, maxHeight);

          createGifFromImages(images, maxWidth, maxHeight);
        }
      };

      img.onerror = () => {
        loadedImages++;
        console.error(`Erreur lors du chargement de l'image ${sourceImage.url}`);

        // Si toutes les images ont été traitées (même avec des erreurs), continuer
        if (loadedImages === totalImages && images.some(img => img)) {
          // Filtrer les images qui n'ont pas pu être chargées
          const validImages = images.filter(img => img);

          if (validImages.length >= 2) {
            createGifFromImages(validImages, maxWidth, maxHeight);
          } else {
            gifResult.innerHTML = '<div class="error">Pas assez d\'images valides pour créer un GIF</div>';
          }
        }
      };

      img.src = sourceImage.url;
    });
  }

  // Fonction pour dupliquer un bloc GIF
  function duplicateGifBlock(blockId) {
    const originalBlock = canvasData.blocks.find(block => block.id === blockId);
    if (!originalBlock) return;

    const originalElement = document.querySelector(`.gif-block[data-block-id="${blockId}"]`);
    if (!originalElement) return;

    // Calculer la nouvelle position
    const newX = parseInt(originalElement.style.left) + 50;
    const newY = parseInt(originalElement.style.top) + 50;

    // Créer le nouveau bloc
    createGifBlock({
      x: newX,
      y: newY,
      sourceImages: [...originalBlock.sourceImages],
      gifUrl: originalBlock.gifUrl,
      delay: originalBlock.delay,
      fitMode: originalBlock.fitMode || 'contain'
    });
  }

  // Ajouter des gestionnaires d'événements pour les boutons d'image dans les blocs de prompts
  function setupGenerateImageFromPrompt(blockElement) {
    const blockId = blockElement.dataset.blockId;

    // 1. Bouton pour générer une nouvelle image à partir du prompt
    const generateImageButton = blockElement.querySelector('.generate-image');
    if (generateImageButton) {
      generateImageButton.addEventListener('click', () => {
        const promptInput = blockElement.querySelector('.prompt-input');
        const prompt = promptInput.value.trim();

        if (!prompt) {
          alert('Veuillez entrer un prompt avant de générer une image.');
          return;
        }

        // Créer un nouveau bloc d'image avec le prompt du bloc de texte
        // La position sera automatiquement calculée par createImageBlock
        createImageBlock({
          prompt,
          parentId: blockId
        });
      });
    }

    // 2. Bouton pour régénérer une image existante à partir du prompt
    const regenerateImageFromPromptButton = blockElement.querySelector('.regenerate-image-from-prompt');
    if (regenerateImageFromPromptButton) {
      regenerateImageFromPromptButton.addEventListener('click', () => {
        const promptInput = blockElement.querySelector('.prompt-input');
        const prompt = promptInput.value.trim();

        if (!prompt) {
          alert('Veuillez entrer un prompt avant de régénérer une image.');
          return;
        }

        // Vérifier si un bloc d'image lié à ce prompt existe déjà
        const linkedImageBlock = canvasData.blocks.find(block =>
          block.type === 'image' &&
          block.parentId === blockId &&
          !block.linkedToResponseOf
        );

        if (linkedImageBlock) {
          // Mettre à jour le bloc d'image existant
          const imageBlockElement = document.querySelector(`.image-block[data-block-id="${linkedImageBlock.id}"]`);
          if (imageBlockElement) {
            const promptInput = imageBlockElement.querySelector('.image-prompt-input');
            const imageContainer = imageBlockElement.querySelector('.image-result');
            const modelSelector = imageBlockElement.querySelector('.image-model-selector');
            const selectedModel = modelSelector.value;

            // Mettre à jour le prompt
            promptInput.value = prompt;

            // Afficher l'indicateur de chargement
            imageContainer.innerHTML = '<div class="loading"></div>';

            // Générer une nouvelle image
            const imageUrl = pollinationsAPI.generateImage(prompt, selectedModel);

            // Créer l'élément image
            const img = new Image();
            img.alt = prompt;

            // Gérer le chargement de l'image
            img.onload = () => {
              // Afficher l'image
              imageContainer.innerHTML = '';
              imageContainer.appendChild(img);

              // Mettre à jour les données du bloc
              const blockIndex = canvasData.blocks.findIndex(block => block.id === linkedImageBlock.id);
              if (blockIndex !== -1) {
                canvasData.blocks[blockIndex].prompt = prompt;
                canvasData.blocks[blockIndex].imageUrl = imageUrl;
              }
            };

            img.onerror = () => {
              imageContainer.innerHTML = '<div class="error">Erreur lors du chargement de l\'image</div>';
            };

            // Déclencher le chargement de l'image
            img.src = imageUrl;

            // Ajouter une animation pour indiquer la mise à jour
            imageBlockElement.classList.add('updating');
            setTimeout(() => {
              imageBlockElement.classList.remove('updating');
            }, 1000);
          }
        } else {
          // Si aucun bloc d'image lié n'existe, créer un nouveau bloc
          alert('Aucun bloc d\'image lié à ce prompt n\'existe. Un nouveau bloc sera créé.');

          createImageBlock({
            prompt,
            parentId: blockId
          });
        }
      });
    }

    // 3. Bouton pour générer une nouvelle image à partir de la réponse
    const generateImageFromResponseButton = blockElement.querySelector('.generate-image-from-response');
    if (generateImageFromResponseButton) {
      generateImageFromResponseButton.addEventListener('click', () => {
        const responseContent = blockElement.querySelector('.response-content');
        const response = stripHtml(responseContent.innerHTML).trim();

        if (!response) {
          alert('Veuillez d\'abord obtenir une réponse avant de générer une image.');
          return;
        }

        // Créer un nouveau bloc d'image avec la réponse du bloc de texte
        createImageBlock({
          prompt: response.substring(0, 500), // Limiter la longueur du prompt pour l'image
          parentId: blockId,
          linkedToResponseOf: blockId // Marquer comme lié à la réponse de ce bloc
        });
      });
    }

    // 4. Bouton pour régénérer une image existante à partir de la réponse
    const regenerateImageFromResponseButton = blockElement.querySelector('.regenerate-image-from-response');
    if (regenerateImageFromResponseButton) {
      regenerateImageFromResponseButton.addEventListener('click', () => {
        const responseContent = blockElement.querySelector('.response-content');
        const response = stripHtml(responseContent.innerHTML).trim();

        if (!response) {
          alert('Veuillez d\'abord obtenir une réponse avant de régénérer une image.');
          return;
        }

        // Vérifier si un bloc d'image lié à cette réponse existe déjà
        const existingImageBlock = canvasData.blocks.find(block =>
          block.type === 'image' &&
          block.linkedToResponseOf === blockId
        );

        if (existingImageBlock) {
          // Mettre à jour le bloc d'image existant
          const imageBlockElement = document.querySelector(`.image-block[data-block-id="${existingImageBlock.id}"]`);
          if (imageBlockElement) {
            const promptInput = imageBlockElement.querySelector('.image-prompt-input');
            const imageContainer = imageBlockElement.querySelector('.image-result');
            const modelSelector = imageBlockElement.querySelector('.image-model-selector');
            const selectedModel = modelSelector.value;

            // Mettre à jour le prompt avec la nouvelle réponse
            const newPrompt = response.substring(0, 500);
            promptInput.value = newPrompt;

            // Afficher l'indicateur de chargement
            imageContainer.innerHTML = '<div class="loading"></div>';

            // Générer une nouvelle image
            const imageUrl = pollinationsAPI.generateImage(newPrompt, selectedModel);

            // Créer l'élément image
            const img = new Image();
            img.alt = newPrompt;

            // Gérer le chargement de l'image
            img.onload = () => {
              // Afficher l'image
              imageContainer.innerHTML = '';
              imageContainer.appendChild(img);

              // Mettre à jour les données du bloc
              const blockIndex = canvasData.blocks.findIndex(block => block.id === existingImageBlock.id);
              if (blockIndex !== -1) {
                canvasData.blocks[blockIndex].prompt = newPrompt;
                canvasData.blocks[blockIndex].imageUrl = imageUrl;
              }
            };

            img.onerror = () => {
              imageContainer.innerHTML = '<div class="error">Erreur lors du chargement de l\'image</div>';
            };

            // Déclencher le chargement de l'image
            img.src = imageUrl;

            // Ajouter une animation pour indiquer la mise à jour
            imageBlockElement.classList.add('updating');
            setTimeout(() => {
              imageBlockElement.classList.remove('updating');
            }, 1000);
          }
        } else {
          // Si aucun bloc d'image lié n'existe, créer un nouveau bloc
          alert('Aucun bloc d\'image lié à cette réponse n\'existe. Un nouveau bloc sera créé.');

          createImageBlock({
            prompt: response.substring(0, 500),
            parentId: blockId,
            linkedToResponseOf: blockId
          });
        }
      });
    }
  }

  function generateUniqueId() {
    return 'canvas-' + Date.now() + '-' + Math.floor(Math.random() * 1000);
  }

  // Fonction pour dupliquer un bloc prompt et tous ses blocs branches
  function duplicateBlockWithBranches(blockId) {
    // Trouver le bloc à dupliquer
    const originalBlock = canvasData.blocks.find(block => block.id === blockId);
    if (!originalBlock) return;

    // Créer une map pour stocker les correspondances entre les IDs originaux et les nouveaux IDs
    const idMap = new Map();

    // Trouver l'élément DOM du bloc original
    const originalElement = document.querySelector(`.prompt-block[data-block-id="${blockId}"]`);
    if (!originalElement) return;

    // Calculer la nouvelle position (décalage par rapport à l'original)
    const newX = parseInt(originalElement.style.left) + 50;
    const newY = parseInt(originalElement.style.top) + 50;

    let newBlock;

    // Vérifier si c'est un bloc racine ou un bloc branche
    if (originalBlock.parentId) {
      // C'est un bloc branche, le dupliquer avec le même parent
      newBlock = createPromptBlock({
        x: newX,
        y: newY,
        prompt: originalBlock.prompt,
        response: originalBlock.response,
        parentId: originalBlock.parentId,
        context: originalBlock.context
      });

      // S'assurer que le nouveau bloc est bien un bloc branche
      newBlock.classList.add('branch-block');
      const typeIndicator = newBlock.querySelector('.block-type-indicator');
      if (typeIndicator) {
        typeIndicator.textContent = 'BRANCHE';
      }
    } else {
      // C'est un bloc racine, le dupliquer comme avant
      newBlock = createPromptBlock({
        x: newX,
        y: newY,
        prompt: originalBlock.prompt,
        response: originalBlock.response
      });

      // S'assurer que le nouveau bloc est bien un bloc racine
      newBlock.classList.add('root-block');
      const typeIndicator = newBlock.querySelector('.block-type-indicator');
      if (typeIndicator) {
        typeIndicator.textContent = 'RACINE';
      }
    }

    // Stocker la correspondance d'ID
    idMap.set(originalBlock.id, newBlock.dataset.blockId);

    // S'assurer que les événements sont correctement configurés
    setupBlockEvents(newBlock);
    setupGenerateImageFromPrompt(newBlock);

    // Trouver tous les blocs branches directs du bloc original
    const directBranches = canvasData.blocks.filter(block => block.parentId === blockId);

    // Dupliquer chaque branche directe
    directBranches.forEach(branch => {
      duplicateBranch(branch, idMap);
    });

    // Recréer les connexions entre les blocs dupliqués
    canvasData.connections.forEach(conn => {
      if (idMap.has(conn.sourceId) && idMap.has(conn.targetId)) {
        createConnection(idMap.get(conn.sourceId), idMap.get(conn.targetId));
      }
    });

    // Mettre à jour les connexions pour le nouveau bloc
    updateConnections(newBlock.dataset.blockId);

    // Sélectionner le nouveau bloc
    clearSelection();
    toggleBlockSelection(newBlock);

    // Mettre à jour les contextes des blocs branches
    setTimeout(() => {
      const newBlockId = idMap.get(blockId);
      const newBlockElement = document.querySelector(`.prompt-block[data-block-id="${newBlockId}"]`);
      if (newBlockElement) {
        const promptInput = newBlockElement.querySelector('.prompt-input');
        if (promptInput) {
          updateChildBlocksContext(newBlockId, promptInput.value);
        }

        const responseContent = newBlockElement.querySelector('.response-content');
        if (responseContent) {
          updateChildBlocksResponseContext(newBlockId, responseContent.innerHTML);
        }
      }
    }, 100);
  }

  // Fonction pour dupliquer une branche (récursive)
  function duplicateBranch(branch, idMap) {
    // Trouver l'élément DOM du bloc original
    const originalElement = document.querySelector(`[data-block-id="${branch.id}"]`);
    if (!originalElement) return;

    // Déterminer le type de bloc
    const isImageBlock = branch.type === 'image';

    // Obtenir le nouveau parentId à partir de la map
    const newParentId = idMap.get(branch.parentId);

    // Si le parent n'est pas trouvé dans la map, ne pas continuer
    if (!newParentId) {
      console.error(`Parent ID ${branch.parentId} non trouvé dans la map pour la branche ${branch.id}`);
      return;
    }

    // Trouver le nouveau bloc parent
    const newParentElement = document.querySelector(`[data-block-id="${newParentId}"]`);
    if (!newParentElement) {
      console.error(`Élément parent avec ID ${newParentId} non trouvé pour la branche ${branch.id}`);
      return;
    }

    // Calculer la nouvelle position
    let newX, newY;

    if (isImageBlock) {
      // Pour les blocs d'images, les positionner près de leur parent
      const parentRect = newParentElement.getBoundingClientRect();
      const canvasRect = canvas.getBoundingClientRect();

      // Positionner à droite du parent avec un petit décalage
      newX = parseInt(newParentElement.style.left) + newParentElement.offsetWidth + 20;
      newY = parseInt(newParentElement.style.top); // Même hauteur que le parent
    } else {
      // Pour les blocs de prompt, utiliser un décalage standard
      newX = parseInt(originalElement.style.left) + 50;
      newY = parseInt(originalElement.style.top) + 50;
    }

    // Mettre à jour le contexte si nécessaire
    let updatedContext = null;
    if (branch.context) {
      // Trouver le bloc parent original et le nouveau bloc parent
      const originalParent = canvasData.blocks.find(b => b.id === branch.parentId);
      const newParent = canvasData.blocks.find(b => b.id === newParentId);

      if (originalParent && newParent) {
        updatedContext = {
          parentPrompt: newParent.prompt || originalParent.prompt,
          parentResponse: newParent.response || originalParent.response
        };
      } else {
        updatedContext = {
          parentPrompt: branch.context.parentPrompt,
          parentResponse: branch.context.parentResponse
        };
      }
    }

    // Créer le nouveau bloc
    let newBlock;
    if (isImageBlock) {
      newBlock = createImageBlock({
        x: newX,
        y: newY,
        prompt: branch.prompt,
        imageUrl: branch.imageUrl,
        parentId: newParentId,
        model: branch.model,
        linkedToResponseOf: branch.linkedToResponseOf ? idMap.get(branch.linkedToResponseOf) : null
      });
    } else {
      newBlock = createPromptBlock({
        x: newX,
        y: newY,
        prompt: branch.prompt,
        response: branch.response,
        parentId: newParentId,
        context: updatedContext
      });
    }

    // Stocker la correspondance d'ID
    idMap.set(branch.id, newBlock.dataset.blockId);

    // S'assurer que les événements sont correctement configurés
    if (isImageBlock) {
      setupImageBlockEvents(newBlock);
    } else {
      setupBlockEvents(newBlock);
      setupGenerateImageFromPrompt(newBlock);
    }

    // Trouver toutes les branches de cette branche
    const subBranches = canvasData.blocks.filter(block => block.parentId === branch.id);

    // Dupliquer chaque sous-branche
    subBranches.forEach(subBranch => {
      duplicateBranch(subBranch, idMap);
    });

    // Mettre à jour les connexions pour ce bloc
    updateConnections(newBlock.dataset.blockId);
  }

  // Fonction pour dupliquer un bloc image
  function duplicateImageBlock(blockId) {
    // Trouver le bloc à dupliquer
    const originalBlock = canvasData.blocks.find(block => block.id === blockId);
    if (!originalBlock) return;

    // Trouver l'élément DOM du bloc original
    const originalElement = document.querySelector(`.image-block[data-block-id="${blockId}"]`);
    if (!originalElement) return;

    // Calculer la nouvelle position
    let newX, newY;

    if (originalBlock.parentId) {
      // Si c'est un bloc branche, le positionner près de son parent
      const parentElement = document.querySelector(`[data-block-id="${originalBlock.parentId}"]`);
      if (parentElement) {
        // Positionner à droite du parent avec un petit décalage
        newX = parseInt(parentElement.style.left) + parentElement.offsetWidth + 20;
        newY = parseInt(parentElement.style.top); // Même hauteur que le parent
      } else {
        // Fallback si le parent n'est pas trouvé
        newX = parseInt(originalElement.style.left) + 50;
        newY = parseInt(originalElement.style.top) + 50;
      }
    } else {
      // Si c'est un bloc racine, utiliser un décalage standard
      newX = parseInt(originalElement.style.left) + 50;
      newY = parseInt(originalElement.style.top) + 50;
    }

    // Créer le nouveau bloc
    const newBlock = createImageBlock({
      x: newX,
      y: newY,
      prompt: originalBlock.prompt,
      imageUrl: originalBlock.imageUrl,
      parentId: originalBlock.parentId,
      model: originalBlock.model,
      linkedToResponseOf: originalBlock.linkedToResponseOf
    });

    // S'assurer que le nouveau bloc a les bonnes classes
    if (!originalBlock.parentId) {
      newBlock.classList.add('root-block');
      const typeIndicator = newBlock.querySelector('.block-type-indicator');
      if (typeIndicator) {
        typeIndicator.textContent = 'RACINE';
      }
    } else {
      newBlock.classList.add('branch-block');
      const typeIndicator = newBlock.querySelector('.block-type-indicator');
      if (typeIndicator) {
        typeIndicator.textContent = 'BRANCHE';
      }
    }

    // S'assurer que les événements sont correctement configurés
    setupImageBlockEvents(newBlock);

    // Mettre à jour les connexions
    if (originalBlock.parentId) {
      createConnection(originalBlock.parentId, newBlock.dataset.blockId);
    }

    // Sélectionner le nouveau bloc
    clearSelection();
    toggleBlockSelection(newBlock);
  }

  // Fonction pour mettre à jour la position verticale de tous les blocs enfants
  function updateChildrenVerticalPosition(parentId, deltaY) {
    console.log(`Mise à jour des enfants de ${parentId} avec deltaY=${deltaY}`);

    // Trouver tous les blocs qui ont ce bloc comme parent (direct ou indirect)
    const allChildren = [];

    // Fonction récursive pour trouver tous les descendants
    function findAllDescendants(currentId) {
      // Trouver tous les blocs enfants directs
      const directChildren = canvasData.blocks.filter(block => block.parentId === currentId);

      directChildren.forEach(child => {
        allChildren.push(child);
        // Trouver récursivement les descendants de ce bloc
        findAllDescendants(child.id);
      });
    }

    // Trouver tous les descendants
    findAllDescendants(parentId);

    console.log(`Nombre d'enfants trouvés: ${allChildren.length}`);

    // Mettre à jour la position de tous les descendants
    allChildren.forEach(child => {
      // Mettre à jour les données du bloc
      child.y += deltaY;

      // Mettre à jour l'élément DOM
      let childElement;
      if (child.type === 'image') {
        childElement = document.querySelector(`.image-block[data-block-id="${child.id}"]`);
      } else {
        childElement = document.querySelector(`.prompt-block[data-block-id="${child.id}"]`);
      }

      if (childElement) {
        childElement.style.top = `${child.y}px`;
        console.log(`Bloc ${child.id} déplacé à Y=${child.y}`);
      } else {
        console.log(`Élément DOM pour le bloc ${child.id} non trouvé`);
      }
    });

    // Mettre à jour toutes les connexions
    canvasData.connections.forEach(conn => {
      if (conn.sourceId === parentId || allChildren.some(child => child.id === conn.sourceId || child.id === conn.targetId)) {
        drawConnection(conn.sourceId, conn.targetId);
      }
    });
  }

  // Fonction pour initialiser le menu contextuel
  function initContextMenu() {
    // Créer le menu contextuel
    contextMenu = document.createElement('div');
    contextMenu.className = 'context-menu';
    contextMenu.style.display = 'none';
    document.body.appendChild(contextMenu);

    // Ajouter les options du menu
    const selectWithChildrenItem = document.createElement('div');
    selectWithChildrenItem.className = 'context-menu-item';
    selectWithChildrenItem.textContent = 'Sélectionner avec descendants';
    selectWithChildrenItem.addEventListener('click', () => {
      if (contextMenu.dataset.blockId) {
        selectBlockWithChildren(contextMenu.dataset.blockId);
      }
      hideContextMenu();
    });

    const duplicateWithChildrenItem = document.createElement('div');
    duplicateWithChildrenItem.className = 'context-menu-item';
    duplicateWithChildrenItem.textContent = 'Dupliquer avec descendants';
    duplicateWithChildrenItem.addEventListener('click', () => {
      if (contextMenu.dataset.blockId) {
        const blockId = contextMenu.dataset.blockId;
        duplicateBlockWithBranches(blockId);
      }
      hideContextMenu();
    });

    contextMenu.appendChild(selectWithChildrenItem);
    contextMenu.appendChild(duplicateWithChildrenItem);

    // Ajouter un gestionnaire d'événements pour le clic droit sur les blocs
    document.addEventListener('contextmenu', (e) => {
      const blockElement = e.target.closest('.prompt-block, .image-block');
      if (blockElement) {
        e.preventDefault();

        // Stocker l'ID du bloc
        const blockId = blockElement.dataset.blockId;
        contextMenu.dataset.blockId = blockId;

        // Positionner et afficher le menu
        contextMenu.style.left = `${e.clientX}px`;
        contextMenu.style.top = `${e.clientY}px`;
        contextMenu.style.display = 'block';
      }
    });

    // Cacher le menu contextuel lors d'un clic ailleurs
    document.addEventListener('click', hideContextMenu);

    // Cacher le menu contextuel lors d'un défilement
    document.addEventListener('scroll', hideContextMenu);
  }

  // Fonction pour cacher le menu contextuel
  function hideContextMenu() {
    if (contextMenu) {
      contextMenu.style.display = 'none';
    }
  }

  // Fonction pour générer un identifiant unique
  function generateUniqueId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
  }

  // Fonction pour créer un template de storyboard avec 50 scènes
  function createStoryboardTemplate() {
    if (!confirm("Vous êtes sur le point de créer un template de storyboard avec 50 scènes. Cela va créer de nombreux blocs. Continuer ?")) {
      return;
    }

    // Réinitialiser le canvas
    document.querySelectorAll('.prompt-block, .image-block, .gif-block, .block-connection').forEach(el => el.remove());
    canvasData.blocks = [];
    canvasData.connections = [];

    // Mettre à jour le nom du canvas
    canvasName = "Storyboard 50 scènes";
    canvasNameInput.value = canvasName;

    // Créer le bloc racine
    const rootBlock = createPromptBlock({
      x: 100,
      y: 100,
      prompt: "Écrivez ici votre scénario complet. Ce bloc servira de source pour extraire les 50 scènes individuelles."
    });

    const rootBlockId = rootBlock.dataset.blockId;

    // Créer 50 blocs branches pour les scènes
    const branchBlocks = [];
    const imageBlocks = [];

    for (let i = 1; i <= 50; i++) {
      // Calculer la position du bloc
      const row = Math.floor((i - 1) / 5);
      const col = (i - 1) % 5;

      const x = 500 + col * 350;
      const y = 100 + row * 250;

      // Créer le bloc branche
      const branchBlock = createPromptBlock({
        x,
        y,
        prompt: `Extrait uniquement le texte exact de la scène ${i} du scénario principal.`,
        parentId: rootBlockId,
        context: {
          parentPrompt: rootBlock.querySelector('.prompt-input').value,
          parentResponse: ""
        }
      });

      branchBlocks.push(branchBlock);

      // Créer un bloc image associé à chaque scène
      const imageBlock = createImageBlock({
        x: x + 350,
        y,
        prompt: `Image de la scène ${i}`,
        parentId: branchBlock.dataset.blockId
      });

      imageBlocks.push(imageBlock);
    }

    // Créer un bloc GIF qui regroupera toutes les images
    setTimeout(() => {
      // Attendre que tous les blocs soient créés
      // Sélectionner tous les blocs d'images
      clearSelection();

      imageBlocks.forEach(imageBlock => {
        const blockId = imageBlock.dataset.blockId;
        selectedBlocks.push(blockId);
        imageBlock.classList.add('selected');
      });

      // Créer le GIF
      createGifFromSelectedImages();

      // Désélectionner tous les blocs
      setTimeout(() => {
        clearSelection();

        // Afficher un message de confirmation
        alert("Template de storyboard créé avec succès !\n\n1. Remplissez le bloc racine avec votre scénario complet\n2. Utilisez les blocs branches pour extraire chaque scène\n3. Générez des images pour chaque scène\n4. Le bloc GIF animé regroupera automatiquement toutes les images");
      }, 500);
    }, 1000);
  }
});
