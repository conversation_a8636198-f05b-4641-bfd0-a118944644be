/* Styles généraux */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
  background-color: #f5f5f5;
  color: #333;
}

/* Styles pour le popup */
.popup-container {
  width: 350px;
  padding: 20px;
}

.popup-container h1 {
  font-size: 18px;
  margin-bottom: 15px;
  color: #2c3e50;
  text-align: center;
}

.settings {
  margin-bottom: 20px;
  background-color: #fff;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.setting-group {
  margin-bottom: 10px;
}

.setting-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.setting-group select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #fff;
}

.buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.buttons button {
  flex: 1;
  padding: 10px;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.buttons button:hover {
  background-color: #2980b9;
}

.saved-canvases {
  background-color: #fff;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.saved-canvases h2 {
  font-size: 16px;
  margin-bottom: 10px;
  color: #2c3e50;
}

#canvas-list {
  list-style: none;
}

#canvas-list li {
  padding: 8px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

#canvas-list li:hover {
  background-color: #f0f0f0;
}

#canvas-list li .button-group {
  display: flex;
  gap: 5px;
}

#canvas-list li button {
  border: none;
  border-radius: 4px;
  padding: 3px 6px;
  cursor: pointer;
  color: white;
}

#canvas-list li button.delete-button {
  background-color: #e74c3c;
}

#canvas-list li button.duplicate-button {
  background-color: #3498db;
}

/* Styles pour la page canvas */
.toolbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background-color: #fff;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  padding: 0 20px;
  z-index: 100;
}

.tool-group {
  display: flex;
  align-items: center;
  margin-right: 20px;
}

.tool-button {
  background-color: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px 12px;
  margin-right: 5px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: all 0.2s;
}

.tool-button:hover {
  background-color: #e9ecef;
}

.tool-button .icon {
  margin-right: 5px;
}

/* Styles pour le menu déroulant des templates */
.template-dropdown {
  position: relative;
  display: inline-block;
}

.template-dropdown-content {
  display: none;
  position: absolute;
  background-color: #f9f9f9;
  min-width: 200px;
  box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
  z-index: 1;
  border-radius: 4px;
}

.template-dropdown-content a {
  color: black;
  padding: 12px 16px;
  text-decoration: none;
  display: block;
}

.template-dropdown-content a:hover {
  background-color: #f1f1f1;
}

.template-dropdown:hover .template-dropdown-content {
  display: block;
}

.tool-select, .tool-input {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #fff;
}

#canvas-container {
  position: absolute;
  top: 60px;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  background-color: #f0f0f0;
  background-image:
    linear-gradient(rgba(200, 200, 200, 0.5) 1px, transparent 1px),
    linear-gradient(90deg, rgba(200, 200, 200, 0.5) 1px, transparent 1px);
  background-size: 20px 20px;
}

#canvas {
  position: absolute;
  width: 5000px;
  height: 5000px;
  transform-origin: 0 0;
  transform: scale(1);
  transition: transform 0.1s;
}

/* Styles communs pour les blocs */
.prompt-block, .image-block, .gif-block {
  position: absolute;
  width: 300px;
  min-height: 200px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  resize: both;
}

.prompt-block-header, .image-block-header, .gif-block-header {
  padding: 10px;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: move;
}

.prompt-block-header {
  background-color: #3498db;
}

.image-block-header {
  background-color: #e74c3c;
}

.gif-block-header {
  background-color: #9b59b6;
}

.prompt-block-title, .image-block-title, .gif-block-title {
  font-weight: 500;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.block-title-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.prompt-block-controls button, .image-block-controls button, .gif-block-controls button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 16px;
  margin-left: 5px;
}

.duplicate-block {
  font-size: 14px !important;
  font-weight: bold;
}

.duplicate-block:hover {
  color: #f1c40f !important;
}

.prompt-block-content, .image-block-content, .gif-block-content {
  flex: 1;
  padding: 10px;
  display: flex;
  flex-direction: column;
}

.prompt-input, .image-prompt-input {
  width: 100%;
  min-height: 80px;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  resize: vertical;
  margin-bottom: 10px;
}

.prompt-actions, .image-actions, .response-actions, .image-prompt-actions, .parent-context-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 10px;
}

.prompt-actions button, .image-actions button, .response-actions button, .image-prompt-actions button, .parent-context-actions button {
  padding: 6px 12px;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9em;
  flex: 1;
  min-width: 140px;
  text-align: center;
}

.response-actions {
  margin-top: -5px;
  margin-bottom: 10px;
}

.response-actions button {
  background-color: #9b59b6;
  font-size: 0.9em;
}

.response-actions button:hover {
  background-color: #8e44ad;
}

.prompt-actions button {
  background-color: #3498db;
}

.prompt-actions button:hover {
  background-color: #2980b9;
}

.image-prompt-actions button {
  background-color: #e74c3c;
}

.image-prompt-actions button:hover {
  background-color: #c0392b;
}

.image-actions button {
  background-color: #e74c3c;
}

.image-actions button:hover {
  background-color: #c0392b;
}

.response-container, .image-container {
  flex: 1;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 4px;
  overflow-y: auto;
  max-height: 200px;
  transition: background-color 0.3s ease;
}

.response-content.updating {
  animation: response-updating 1s ease;
}

@keyframes response-updating {
  0% { background-color: #f9f9f9; }
  50% { background-color: #fff3e0; }
  100% { background-color: #f9f9f9; }
}

.image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.image-result img {
  max-width: 100%;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.prompt-block-resize-handle, .image-block-resize-handle, .gif-block-resize-handle {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 15px;
  height: 15px;
  cursor: nwse-resize;
}

.prompt-block-resize-handle {
  background: linear-gradient(135deg, transparent 50%, #3498db 50%);
}

.image-block-resize-handle {
  background: linear-gradient(135deg, transparent 50%, #e74c3c 50%);
}

.gif-block-resize-handle {
  background: linear-gradient(135deg, transparent 50%, #9b59b6 50%);
}

/* Styles spécifiques pour les blocs d'images */
.image-options {
  display: flex;
  gap: 5px;
  margin-bottom: 10px;
}

.image-model-selector {
  flex: 1;
  padding: 6px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.refresh-models {
  background-color: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 6px 10px;
  cursor: pointer;
}

.refresh-models:hover {
  background-color: #e9ecef;
}

.generate-image {
  background-color: #e74c3c !important;
}

.generate-image:hover {
  background-color: #c0392b !important;
}

.regenerate-response {
  background-color: #f39c12 !important;
}

.regenerate-response:hover {
  background-color: #d35400 !important;
}

/* Styles pour les blocs GIF */
.gif-info {
  margin-bottom: 10px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.gif-source-info {
  margin-bottom: 10px;
  font-weight: 500;
}

.gif-settings {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;
}

.gif-setting-row {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

.gif-settings label {
  margin-right: 5px;
  min-width: 150px;
}

.gif-fit-mode {
  flex: 1;
  padding: 5px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.gif-delay {
  width: 80px;
  padding: 5px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.regenerate-gif-btn {
  padding: 6px 12px;
  background-color: #9b59b6;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.regenerate-gif-btn:hover {
  background-color: #8e44ad;
}

.gif-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  background-color: #f9f9f9;
  border-radius: 4px;
  margin-bottom: 10px;
  padding: 10px;
  overflow: hidden;
}

.gif-result {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.gif-result img {
  max-width: 100%;
  max-height: 300px;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  object-fit: contain;
}

.source-images-container {
  margin-top: 10px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.source-images-container h4 {
  margin-bottom: 10px;
  font-size: 14px;
  color: #666;
}

.source-images-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.source-image-item {
  position: relative;
  width: 60px;
  height: 60px;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #ddd;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f8f9fa;
}

.source-image-item img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.source-image-remove {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 16px;
  height: 16px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 10px;
  cursor: pointer;
  color: #e74c3c;
}

.source-image-remove:hover {
  background-color: rgba(255, 255, 255, 1);
  color: #c0392b;
}

/* Styles pour les connexions entre blocs */
.block-connection {
  position: absolute;
  pointer-events: none;
  z-index: 50;
}

.connection-line {
  position: absolute;
  background-color: #3498db;
  height: 3px;
  transform-origin: 0 0;
  z-index: 0;
  pointer-events: none;
  box-shadow: 0 0 5px rgba(52, 152, 219, 0.7);
}

/* Styles pour les blocs racines et branches */
.root-block .prompt-block-header {
  background-color: #3498db;
}

.branch-block .prompt-block-header {
  background-color: #9b59b6;
}

/* Styles pour les indicateurs de type de bloc */
.block-type-indicator {
  display: inline-block;
  padding: 2px 6px;
  margin-right: 8px;
  border-radius: 4px;
  font-size: 0.8em;
  font-weight: bold;
  color: white;
}

.root-block .block-type-indicator {
  background-color: #2980b9;
}

.branch-block .block-type-indicator {
  background-color: #8e44ad;
}

/* Styles pour les boutons de contexte parent */
.parent-context-actions {
  display: none; /* Caché par défaut */
}

.branch-block .parent-context-actions {
  display: flex; /* Visible uniquement dans les blocs branches */
}

.parent-context-actions button {
  background-color: #9b59b6;
}

.parent-context-actions button:hover {
  background-color: #8e44ad;
}

/* Styles pour l'indicateur de contexte */
.context-indicator, .link-indicator {
  background-color: #f8f9fa;
  border: 1px dashed #ddd;
  border-radius: 4px;
  padding: 8px;
  margin-bottom: 10px;
  font-size: 12px;
  color: #666;
  display: flex;
  align-items: flex-start;
  line-height: 1.4;
  max-height: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  word-break: break-word;
}

.context-icon, .link-icon {
  margin-right: 5px;
  font-size: 14px;
}

.context-active {
  background-color: #e8f4fd;
  border-color: #3498db;
  color: #2980b9;
}

/* Styles pour l'indicateur de lien */
.link-indicator {
  background-color: #fff3e0;
  border-color: #ffb74d;
  color: #e65100;
}

/* Animation de mise à jour */
@keyframes updating-pulse {
  0% { box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(231, 76, 60, 0); }
  100% { box-shadow: 0 0 0 0 rgba(231, 76, 60, 0); }
}

.updating {
  animation: updating-pulse 1s ease-out;
}

/* Animation de chargement */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #3498db;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Styles pour la sélection */
.selection-box {
  position: absolute;
  border: 2px dashed #3498db;
  background-color: rgba(52, 152, 219, 0.1);
  pointer-events: none;
  z-index: 100;
}

.prompt-block.selected, .image-block.selected, .gif-block.selected {
  box-shadow: 0 0 0 3px #3498db, 0 3px 10px rgba(0, 0, 0, 0.2);
  outline: 2px solid #3498db;
  z-index: 5 !important;
}

.prompt-block.selected .prompt-block-header,
.image-block.selected .image-block-header,
.gif-block.selected .gif-block-header {
  background-color: #2980b9;
}

.prompt-block.selected.branch-block .prompt-block-header {
  background-color: #8e44ad;
}

.image-block.selected .image-block-header {
  background-color: #c0392b;
}

.gif-block.selected .gif-block-header {
  background-color: #8e44ad;
}

/* Curseur pour le déplacement du canvas */
#canvas-container {
  cursor: default;
}

#canvas-container:active {
  cursor: grabbing;
}

/* Styles pour l'aide à la navigation */
.navigation-help {
  flex-grow: 1;
  display: flex;
  justify-content: flex-end;
}

.navigation-tip {
  background-color: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 0.9em;
  color: #666;
  display: flex;
  align-items: center;
}

.navigation-tip .icon {
  margin-right: 5px;
  font-size: 1.1em;
}

/* Menu contextuel */
.context-menu {
  position: absolute;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  padding: 5px 0;
  z-index: 1000;
}

.context-menu-item {
  padding: 8px 15px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
}

.context-menu-item:hover {
  background-color: #f5f5f5;
}
