<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Canvas AI - Tableau Blanc</title>
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <div class="toolbar">
    <div class="tool-group">
      <button id="add-prompt-block" class="tool-button" title="Ajouter un bloc prompt">
        <span class="icon">+</span> Bloc Prompt
      </button>
      <button id="add-image-block" class="tool-button" title="Ajouter un bloc image IA">
        <span class="icon">🖼️</span> Bloc Image IA
      </button>
      <button id="create-gif" class="tool-button" title="Créer un GIF animé à partir des images sélectionnées">
        <span class="icon">🎬</span> Créer GIF Animé
      </button>
      <div class="template-dropdown">
        <button id="template-button" class="tool-button" title="Créer un template prédéfini">
          <span class="icon">📋</span> Template
        </button>
        <div class="template-dropdown-content">
          <a href="#" id="template-storyboard">Storyboard 50 scènes</a>
          <!-- Autres templates peuvent être ajoutés ici -->
        </div>
      </div>
      <select id="gemini-model-selector" class="tool-select">
        <option value="gemini-1.5-pro">Gemini 1.5 Pro</option>
        <option value="gemini-1.5-flash">Gemini 1.5 Flash</option>
        <option value="gemini-pro">Gemini Pro (1.0)</option>
      </select>
    </div>
    <div class="tool-group">
      <button id="save-canvas" class="tool-button" title="Sauvegarder">
        <span class="icon">💾</span> Sauvegarder
      </button>
      <input type="text" id="canvas-name" placeholder="Nom du canvas" class="tool-input">
    </div>
    <div class="tool-group">
      <button id="zoom-in" class="tool-button" title="Zoom avant">
        <span class="icon">🔍+</span>
      </button>
      <button id="zoom-out" class="tool-button" title="Zoom arrière">
        <span class="icon">🔍-</span>
      </button>
      <button id="reset-view" class="tool-button" title="Réinitialiser la vue">
        <span class="icon">⟲</span>
      </button>
    </div>
    <div class="tool-group navigation-help">
      <div class="navigation-tip" title="Raccourcis clavier">
        <span class="icon">🖱️</span> Bouton milieu: déplacer | Clic gauche: sélectionner | Ctrl+A: tout sélectionner | Ctrl+Shift+A: sélectionner avec descendants | Échap: désélectionner
      </div>
    </div>
  </div>

  <div id="canvas-container">
    <div id="canvas">
      <!-- Les blocs de prompts seront ajoutés ici dynamiquement -->
    </div>
  </div>

  <!-- Template pour les blocs de prompts -->
  <template id="prompt-block-template">
    <div class="prompt-block" data-block-id="">
      <div class="prompt-block-header">
        <div class="prompt-block-title">
          <span class="block-type-indicator"></span>
          <span class="block-title-text">Bloc Prompt</span>
        </div>
        <div class="prompt-block-controls">
          <button class="duplicate-block" title="Dupliquer ce bloc et ses branches">⧉</button>
          <button class="minimize-block">_</button>
          <button class="close-block">×</button>
        </div>
      </div>
      <div class="prompt-block-content">
        <textarea class="prompt-input" placeholder="Entrez votre prompt ici..."></textarea>
        <div class="prompt-actions">
          <button class="send-prompt">Générer réponse IA depuis prompt</button>
          <button class="regenerate-response">Régénérer réponse IA</button>
          <button class="create-branch">Créer une branche</button>
        </div>
        <div class="parent-context-actions">
          <button class="generate-from-parent-prompt">Générer réponse basée sur le texte parent</button>
          <button class="generate-from-parent-response">Générer réponse basée sur la réponse parent</button>
        </div>
        <div class="image-prompt-actions">
          <button class="generate-image">Générer nouvelle image depuis prompt</button>
          <button class="regenerate-image-from-prompt">Régénérer image existante depuis prompt</button>
        </div>
        <div class="response-actions">
          <button class="generate-image-from-response">Générer nouvelle image depuis réponse</button>
          <button class="regenerate-image-from-response">Régénérer image existante depuis réponse</button>
        </div>
        <div class="response-container">
          <div class="response-content"></div>
        </div>
      </div>
      <div class="prompt-block-resize-handle"></div>
    </div>
  </template>

  <!-- Template pour les blocs d'images -->
  <template id="image-block-template">
    <div class="image-block" data-block-id="">
      <div class="image-block-header">
        <div class="image-block-title">
          <span class="block-type-indicator"></span>
          <span class="block-title-text">Bloc Image IA</span>
        </div>
        <div class="image-block-controls">
          <button class="duplicate-block" title="Dupliquer ce bloc">⧉</button>
          <button class="minimize-block">_</button>
          <button class="close-block">×</button>
        </div>
      </div>
      <div class="image-block-content">
        <textarea class="image-prompt-input" placeholder="Décrivez l'image que vous souhaitez générer..."></textarea>
        <div class="image-options">
          <select class="image-model-selector">
            <option value="stable-diffusion">Stable Diffusion</option>
            <option value="midjourney">Midjourney Style</option>
            <option value="dalle">DALL-E Style</option>
          </select>
          <button class="refresh-models" title="Rafraîchir la liste des modèles">⟳</button>
        </div>
        <div class="image-actions">
          <button class="generate-image-btn">Générer Image</button>
          <button class="create-image-branch">Créer une variante</button>
        </div>
        <div class="image-container">
          <div class="image-result"></div>
        </div>
      </div>
      <div class="image-block-resize-handle"></div>
    </div>
  </template>

  <!-- Template pour les blocs GIF animés -->
  <template id="gif-block-template">
    <div class="gif-block" data-block-id="">
      <div class="gif-block-header">
        <div class="gif-block-title">
          <span class="block-type-indicator"></span>
          <span class="block-title-text">GIF Animé</span>
        </div>
        <div class="gif-block-controls">
          <button class="duplicate-block" title="Dupliquer ce bloc">⧉</button>
          <button class="minimize-block">_</button>
          <button class="close-block">×</button>
        </div>
      </div>
      <div class="gif-block-content">
        <div class="gif-info">
          <div class="gif-source-info">Images sources: <span class="source-count">0</span></div>
          <div class="gif-settings">
            <div class="gif-setting-row">
              <label>Délai entre images (ms):</label>
              <input type="number" class="gif-delay" value="500" min="100" max="2000" step="100">
            </div>
            <div class="gif-setting-row">
              <label>Mode d'ajustement:</label>
              <select class="gif-fit-mode">
                <option value="contain">Contenir (voir l'image entière)</option>
                <option value="cover">Couvrir (remplir le cadre)</option>
              </select>
            </div>
            <button class="regenerate-gif-btn">Régénérer GIF</button>
          </div>
        </div>
        <div class="gif-container">
          <div class="gif-result"></div>
        </div>
        <div class="source-images-container">
          <h4>Images sources:</h4>
          <div class="source-images-list"></div>
        </div>
      </div>
      <div class="gif-block-resize-handle"></div>
    </div>
  </template>

  <script src="gemini-api.js"></script>
  <script src="pollinations-api.js"></script>
  <script src="imagen-api.js"></script>
  <script src="gif.js"></script>
  <script src="canvas.js"></script>
</body>
</html>
