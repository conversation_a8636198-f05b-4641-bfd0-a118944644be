class GeminiAPI {
  constructor() {
    this.apiKey = 'AIzaSyBgSik_a6DPbSqz5KOByopEkDRJ6EuOzW0';
    this.baseUrl = 'https://generativelanguage.googleapis.com/v1beta';
    this.currentModel = 'gemini-1.5-pro';
  }

  // Définir le modèle à utiliser
  setModel(model) {
    this.currentModel = model;
  }

  // Obtenir le modèle actuel
  getModel() {
    return this.currentModel;
  }

  // Obtenir la liste des modèles disponibles
  getAvailableModels() {
    return [
      { id: 'gemini-1.5-pro', name: 'Gemini 1.5 Pro' },
      { id: 'gemini-1.5-flash', name: 'Gemini 1.5 Flash' },
      { id: 'gemini-pro', name: 'Gemini Pro (1.0)' }
    ];
  }

  // Envoyer une requête à l'API Gemini
  async generateContent(prompt) {
    try {
      const endpoint = `${this.baseUrl}/models/${this.currentModel}:generateContent?key=${this.apiKey}`;
      
      const requestBody = {
        contents: [
          {
            parts: [
              {
                text: prompt
              }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 2048,
        }
      };

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Erreur API Gemini: ${errorData.error.message || 'Erreur inconnue'}`);
      }

      const data = await response.json();
      
      // Extraire le texte de la réponse
      if (data.candidates && data.candidates.length > 0 && 
          data.candidates[0].content && 
          data.candidates[0].content.parts && 
          data.candidates[0].content.parts.length > 0) {
        return data.candidates[0].content.parts[0].text;
      } else {
        throw new Error('Format de réponse inattendu de l\'API Gemini');
      }
    } catch (error) {
      console.error('Erreur lors de la génération de contenu:', error);
      throw error;
    }
  }
}

// Créer une instance globale de l'API Gemini
const geminiAPI = new GeminiAPI();
