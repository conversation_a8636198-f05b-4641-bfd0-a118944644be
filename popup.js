document.addEventListener('DOMContentLoaded', () => {
  const newCanvasButton = document.getElementById('new-canvas');
  const openCanvasButton = document.getElementById('open-canvas');
  const geminiModelSelect = document.getElementById('gemini-model');
  const canvasList = document.getElementById('canvas-list');

  // Charger les paramètres sauvegardés
  chrome.storage.local.get(['geminiModel'], (result) => {
    if (result.geminiModel) {
      geminiModelSelect.value = result.geminiModel;
    }
  });

  // Sauvegarder les paramètres lorsqu'ils sont modifiés
  geminiModelSelect.addEventListener('change', () => {
    chrome.storage.local.set({ geminiModel: geminiModelSelect.value });
  });

  // Ouvrir un nouveau canvas
  newCanvasButton.addEventListener('click', () => {
    chrome.tabs.create({ url: 'canvas.html' });
  });

  // Ouvrir un canvas existant
  openCanvasButton.addEventListener('click', () => {
    const canvasItems = document.querySelectorAll('#canvas-list li');
    if (canvasItems.length > 0) {
      // Afficher/masquer la liste des canvas
      const savedCanvasesSection = document.getElementById('saved-canvases');
      savedCanvasesSection.style.display = savedCanvasesSection.style.display === 'none' ? 'block' : 'none';
    } else {
      alert('Aucun canvas sauvegardé.');
    }
  });

  // Charger la liste des canvas sauvegardés
  loadSavedCanvases();

  function loadSavedCanvases() {
    chrome.storage.local.get(['savedCanvases'], (result) => {
      const savedCanvases = result.savedCanvases || [];
      canvasList.innerHTML = '';

      if (savedCanvases.length === 0) {
        canvasList.innerHTML = '<li class="empty-list">Aucun canvas sauvegardé</li>';
        return;
      }

      savedCanvases.forEach(canvas => {
        const li = document.createElement('li');
        li.textContent = canvas.name;
        li.dataset.id = canvas.id;

        // Créer un groupe de boutons
        const buttonGroup = document.createElement('div');
        buttonGroup.className = 'button-group';

        // Ajouter un bouton de duplication
        const duplicateButton = document.createElement('button');
        duplicateButton.textContent = '⧉';
        duplicateButton.className = 'duplicate-button';
        duplicateButton.title = 'Dupliquer';
        duplicateButton.addEventListener('click', (e) => {
          e.stopPropagation();
          duplicateCanvas(canvas.id);
        });

        // Ajouter un bouton de suppression
        const deleteButton = document.createElement('button');
        deleteButton.textContent = '×';
        deleteButton.className = 'delete-button';
        deleteButton.title = 'Supprimer';
        deleteButton.addEventListener('click', (e) => {
          e.stopPropagation();
          deleteCanvas(canvas.id);
        });

        // Ajouter les boutons au groupe
        buttonGroup.appendChild(duplicateButton);
        buttonGroup.appendChild(deleteButton);

        // Ajouter le groupe de boutons à l'élément li
        li.appendChild(buttonGroup);

        // Ouvrir le canvas au clic
        li.addEventListener('click', () => {
          chrome.tabs.create({ url: `canvas.html?id=${canvas.id}` });
        });

        canvasList.appendChild(li);
      });
    });
  }

  function deleteCanvas(canvasId) {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce canvas ?')) {
      chrome.storage.local.get(['savedCanvases'], (result) => {
        const savedCanvases = result.savedCanvases || [];
        const updatedCanvases = savedCanvases.filter(canvas => canvas.id !== canvasId);

        chrome.storage.local.set({ savedCanvases: updatedCanvases }, () => {
          loadSavedCanvases();
        });
      });
    }
  }

  function duplicateCanvas(canvasId) {
    chrome.storage.local.get(['savedCanvases'], (result) => {
      const savedCanvases = result.savedCanvases || [];
      const canvasToDuplicate = savedCanvases.find(canvas => canvas.id === canvasId);

      if (!canvasToDuplicate) {
        alert('Canvas introuvable.');
        return;
      }

      // Générer un nouvel ID unique
      const newCanvasId = generateUniqueId();

      // Créer une copie profonde des données du canvas
      const duplicatedData = JSON.parse(JSON.stringify(canvasToDuplicate.data));

      // Créer un nouveau nom pour la copie
      const newCanvasName = `${canvasToDuplicate.name} (copie)`;

      // Créer le nouveau canvas
      const newCanvas = {
        id: newCanvasId,
        name: newCanvasName,
        data: duplicatedData,
        lastModified: new Date().toISOString()
      };

      // Ajouter le nouveau canvas à la liste
      savedCanvases.push(newCanvas);

      // Sauvegarder la liste mise à jour
      chrome.storage.local.set({ savedCanvases }, () => {
        alert(`Canvas "${newCanvasName}" créé avec succès.`);
        loadSavedCanvases();
      });
    });
  }

  function generateUniqueId() {
    return 'canvas-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
  }
});
