class PollinationsAPI {
  constructor() {
    this.baseUrl = 'https://image.pollinations.ai';
    this.currentModel = 'stable-diffusion';
    this.availableModels = [];
    this.fetchModels();
  }

  // Définir le modèle à utiliser
  setModel(model) {
    this.currentModel = model;
  }

  // Obtenir le modèle actuel
  getModel() {
    return this.currentModel;
  }

  // Récupérer la liste des modèles disponibles
  async fetchModels() {
    try {
      const response = await fetch(`${this.baseUrl}/models`);
      if (!response.ok) {
        throw new Error('Erreur lors de la récupération des modèles');
      }
      
      const data = await response.json();
      this.availableModels = data;
      return data;
    } catch (error) {
      console.error('Erreur lors de la récupération des modèles:', error);
      // Utiliser des modèles par défaut en cas d'erreur
      this.availableModels = [
        { id: 'stable-diffusion', name: 'Stable Diffusion' },
        { id: 'midjourney', name: 'Midjourney Style' },
        { id: 'dalle', name: 'DALL-E Style' }
      ];
      return this.availableModels;
    }
  }

  // Générer une image à partir d'un prompt
  generateImage(prompt, model = null) {
    // Utiliser le modèle spécifié ou le modèle actuel
    const selectedModel = model || this.currentModel;
    
    // Encoder le prompt pour l'URL
    const encodedPrompt = encodeURIComponent(prompt);
    
    // Construire l'URL de l'image
    let imageUrl;
    
    if (selectedModel === 'stable-diffusion') {
      imageUrl = `${this.baseUrl}/prompt/${encodedPrompt}`;
    } else {
      // Ajouter le modèle spécifique à l'URL
      imageUrl = `${this.baseUrl}/prompt/${selectedModel}/${encodedPrompt}`;
    }
    
    // Ajouter un paramètre aléatoire pour éviter la mise en cache
    imageUrl += `?random=${Date.now()}`;
    
    return imageUrl;
  }

  // Générer une variante d'une image existante
  generateVariant(prompt, model = null) {
    // Pour les variantes, on ajoute simplement un paramètre aléatoire différent
    // car Pollinations génère des images légèrement différentes à chaque appel
    return this.generateImage(prompt, model);
  }
}

// Créer une instance globale de l'API Pollinations
const pollinationsAPI = new PollinationsAPI();
