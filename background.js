// Initialisation des paramètres par défaut lors de l'installation
chrome.runtime.onInstalled.addListener(() => {
  chrome.storage.local.get(['geminiModel', 'savedCanvases'], (result) => {
    // Initialiser le modèle Gemini par défaut s'il n'existe pas
    if (!result.geminiModel) {
      chrome.storage.local.set({ geminiModel: 'gemini-1.5-pro' });
    }
    
    // Initialiser la liste des canvas sauvegardés s'il n'existe pas
    if (!result.savedCanvases) {
      chrome.storage.local.set({ savedCanvases: [] });
    }
  });
});
